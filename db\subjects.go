package db

import (
	"context"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetSubjects(ctx context.Context, userID uint) ([]models.Subject, error) {
	start := time.Now()
	slog.Debug("Retrieving subjects for user", "user_id", userID)

	var subjects []models.Subject

	// First, get the user to determine their role
	var user models.User
	if err := p.db.Where("id = ?", userID).First(&user).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to find user for GetSubjects",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	slog.Debug("User found for GetSubjects",
		"user_id", userID,
		"role", user.Role,
		"email", user.Email,
	)

	// Check user role and apply appropriate logic
	if user.Role == "Student" {
		slog.Debug("Processing student subject access", "user_id", userID)

		// For students, return subjects from enrolled courses (existing logic)
		var student models.Student
		if err := p.db.Preload("Courses.Subjects").
			Where("user_id = ?", userID).First(&student).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Failed to retrieve student for subjects",
				"user_id", userID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, err
		}

		// Loop through the courses and collect subjects
		for _, course := range student.Courses {
			subjects = append(subjects, course.Subjects...)
		}

		duration := time.Since(start)
		slog.Info("Subjects retrieved successfully for student",
			"user_id", userID,
			"course_count", len(student.Courses),
			"subject_count", len(subjects),
			"duration_ms", duration.Milliseconds(),
		)
	} else {
		slog.Debug("Processing admin subject access", "user_id", userID, "role", user.Role)

		// For admins, return all subjects available in database
		if err := p.db.Find(&subjects).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Failed to retrieve all subjects for admin",
				"user_id", userID,
				"role", user.Role,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, err
		}

		duration := time.Since(start)
		slog.Info("All subjects retrieved successfully for admin",
			"user_id", userID,
			"role", user.Role,
			"subject_count", len(subjects),
			"duration_ms", duration.Milliseconds(),
		)
	}

	return subjects, nil
}

func (p *DbPlugin) CreateSubject(ctx context.Context, subject *models.Subject) (*models.Subject, error) {
	start := time.Now()
	slog.Info("Creating subject",
		"subject_name", subject.Name,
		"subject_display_name", subject.DisplayName,
	)

	res := p.db.Save(subject)
	duration := time.Since(start)

	if res.Error != nil {
		slog.Error("Failed to create subject",
			"subject_name", subject.Name,
			"subject_display_name", subject.DisplayName,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	slog.Info("Subject created successfully",
		"subject_id", subject.ID,
		"subject_name", subject.Name,
		"subject_display_name", subject.DisplayName,
		"duration_ms", duration.Milliseconds(),
	)
	return subject, nil
}
