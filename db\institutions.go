package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// CreateInstitution creates a new institution in the database
func (p *DbPlugin) CreateInstitution(ctx context.Context, institution *models.Institution) (*models.Institution, error) {
	start := time.Now()
	slog.Info("Creating institution",
		"name", institution.Name,
		"city_or_town", institution.CityOrTown,
		"state", institution.State,
	)

	if err := p.db.WithContext(ctx).Create(institution).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to create institution",
			"name", institution.Name,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to create institution: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Institution created successfully",
		"institution_id", institution.ID,
		"name", institution.Name,
		"duration_ms", duration.Milliseconds(),
	)

	return institution, nil
}

// UpdateInstitution updates an existing institution in the database
func (p *DbPlugin) UpdateInstitution(ctx context.Context, institutionID uint, updateData *models.InstitutionForUpdate) (*models.Institution, error) {
	start := time.Now()
	slog.Info("Updating institution",
		"institution_id", institutionID,
		"name", updateData.Name,
	)

	// First check if the institution exists
	var institution models.Institution
	if err := p.db.WithContext(ctx).First(&institution, institutionID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Institution not found for update",
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("institution with ID %d not found: %w", institutionID, err)
	}

	// Update the institution
	updates := map[string]interface{}{
		"name":           updateData.Name,
		"city_or_town":   updateData.CityOrTown,
		"state":          updateData.State,
		"contact_name":   updateData.ContactName,
		"contact_number": updateData.ContactNumber,
	}

	if err := p.db.WithContext(ctx).Model(&institution).Updates(updates).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to update institution",
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to update institution: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Institution updated successfully",
		"institution_id", institutionID,
		"name", institution.Name,
		"duration_ms", duration.Milliseconds(),
	)

	return &institution, nil
}

// DeleteInstitution soft deletes an institution from the database
func (p *DbPlugin) DeleteInstitution(ctx context.Context, institutionID uint) error {
	start := time.Now()
	slog.Info("Deleting institution",
		"institution_id", institutionID,
	)

	// First check if the institution exists
	var institution models.Institution
	if err := p.db.WithContext(ctx).First(&institution, institutionID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Institution not found for deletion",
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("institution with ID %d not found: %w", institutionID, err)
	}

	// Soft delete the institution
	if err := p.db.WithContext(ctx).Delete(&institution).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to delete institution",
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to delete institution: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Institution deleted successfully",
		"institution_id", institutionID,
		"name", institution.Name,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}

// GetInstitutions retrieves all institutions from the database
func (p *DbPlugin) GetInstitutions(ctx context.Context) ([]models.Institution, error) {
	start := time.Now()
	slog.Info("Retrieving all institutions")

	var institutions []models.Institution
	if err := p.db.WithContext(ctx).Order("name ASC").Find(&institutions).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve institutions",
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve institutions: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Institutions retrieved successfully",
		"count", len(institutions),
		"duration_ms", duration.Milliseconds(),
	)

	return institutions, nil
}
