package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"
)

func TestStudentPasswordHashing(t *testing.T) {
	// Skip if noAuth<PERSON>outer is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping student password test")
		return
	}

	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testEmail := "passwordtest_" + timestamp + "@example.com"

	// Clean up before test
	db.Exec("DELETE FROM students WHERE user_id IN (SELECT id FROM users WHERE email = ?)", testEmail)
	db.Exec("DELETE FROM users WHERE email = ?", testEmail)

	// Create student with password
	payload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Password Test Student",
			Email:          testEmail,
			PhoneNumber:    "5555555555",
			ContactAddress: "123 Password Test Street",
		},
		ParentPhone: "555-1234",
		ParentEmail: "passwordparent_" + timestamp + "@example.com",
		Password:    "testpassword123",
	}

	body, _ := json.Marshal(payload)
	req, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	// Verify student was created successfully
	assert.Equal(t, http.StatusOK, resp.Code)

	var res models.CreatedStudentResponse
	err := json.NewDecoder(resp.Body).Decode(&res)
	assert.NoError(t, err)

	// Verify student was created in database
	var student models.Student
	err = db.Preload("User").First(&student, "id = ?", res.Student.ID).Error
	assert.Nil(t, err)

	// Verify password was hashed (should not be the plain text password)
	assert.NotEmpty(t, student.User.PasswordHash)
	assert.NotEqual(t, "testpassword123", student.User.PasswordHash)

	// Verify the hashed password can be verified with bcrypt
	// The password should be combined with the secret key before hashing
	err = bcrypt.CompareHashAndPassword([]byte(student.User.PasswordHash), []byte("testpassword123"+token.SecretKeyStr))
	assert.NoError(t, err, "Password should be correctly hashed and verifiable")

	// Cleanup
	db.Delete(&student)
	db.Delete(&student.User)
}

func TestStudentPasswordValidation(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping student password validation test")
		return
	}

	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	// Test 1: Password too short
	shortPasswordPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Short Password Student",
			Email:          "shortpass_" + timestamp + "@example.com",
			PhoneNumber:    "6666666666",
			ContactAddress: "456 Short Pass Street",
		},
		ParentPhone: "555-5678",
		ParentEmail: "shortpassparent_" + timestamp + "@example.com",
		Password:    "123", // Too short
	}

	body, _ := json.Marshal(shortPasswordPayload)
	req, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	resp := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp, req)

	// Should return bad request due to password validation
	assert.Equal(t, http.StatusBadRequest, resp.Code)

	// Test 2: Missing password
	missingPasswordPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Missing Password Student",
			Email:          "missingpass_" + timestamp + "@example.com",
			PhoneNumber:    "7777777777",
			ContactAddress: "789 Missing Pass Street",
		},
		ParentPhone: "555-9012",
		ParentEmail: "missingpassparent_" + timestamp + "@example.com",
		// Password field intentionally omitted
	}

	body2, _ := json.Marshal(missingPasswordPayload)
	req2, _ := http.NewRequest("POST", "/api/students", bytes.NewBuffer(body2))
	req2.Header.Set("Content-Type", "application/json")

	resp2 := httptest.NewRecorder()
	noAuthRouter.ServeHTTP(resp2, req2)

	// Should return bad request due to missing required password
	assert.Equal(t, http.StatusBadRequest, resp2.Code)
}
