-- Migration: Change formula_cards from subject_id to topic_id
-- This migration updates the formula_cards table to reference topics instead of subjects
-- Run this migration to update existing database schema

BEGIN;

-- Step 1: Add the new topic_id column
ALTER TABLE formula_cards 
ADD COLUMN topic_id INTEGER;

-- Step 2: Migrate data from subject_id to topic_id
-- This assigns each formula card to the first topic within its current subject
-- Topics are ordered by creation date, then by ID for consistency
UPDATE formula_cards 
SET topic_id = (
    SELECT t.id 
    FROM topics t 
    JOIN chapters c ON t.chapter_id = c.id 
    WHERE c.subject_id = formula_cards.subject_id 
    ORDER BY t.created_at ASC, t.id ASC
    LIMIT 1
)
WHERE subject_id IS NOT NULL;

-- Step 3: Verify all formula cards have been assigned a topic_id
-- This should not fail if all subjects have at least one topic
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM formula_cards WHERE topic_id IS NULL) THEN
        RAISE EXCEPTION 'Migration failed: Some formula cards could not be assigned to topics';
    END IF;
END $$;

-- Step 4: Remove the old foreign key constraint on subject_id
ALTER TABLE formula_cards 
DROP CONSTRAINT IF EXISTS fk_formula_cards_subject_id;

-- Step 5: Drop the old subject_id column
ALTER TABLE formula_cards 
DROP COLUMN subject_id;

-- Step 6: Add NOT NULL constraint to topic_id
ALTER TABLE formula_cards 
ALTER COLUMN topic_id SET NOT NULL;

-- Step 7: Add foreign key constraint for topic_id
ALTER TABLE formula_cards 
ADD CONSTRAINT fk_formula_cards_topic_id 
FOREIGN KEY (topic_id) REFERENCES topics(id) ON DELETE CASCADE;

-- Step 8: Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_formula_cards_topic_id ON formula_cards(topic_id);

-- Step 9: Drop old index on subject_id if it exists
DROP INDEX IF EXISTS idx_formula_cards_subject_id;

COMMIT;

-- Post-migration verification queries (run these after migration to verify success):
-- 
-- 1. Check that all formula cards have topic_id:
-- SELECT COUNT(*) FROM formula_cards WHERE topic_id IS NULL;
-- 
-- 2. Verify the relationships look correct:
-- SELECT 
--     fc.name as formula_card_name,
--     t.name as topic_name,
--     c.name as chapter_name,
--     s.name as subject_name
-- FROM formula_cards fc
-- JOIN topics t ON fc.topic_id = t.id
-- JOIN chapters c ON t.chapter_id = c.id
-- JOIN subjects s ON c.subject_id = s.id
-- ORDER BY s.name, c.name, t.name, fc.name
-- LIMIT 10;
