package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"log/slog"
	"os"
	"time"

	"github.com/naughtygopher/errors"

	"ziaacademy-backend/internal/configs"

	gormPostgres "gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// recoverer is used for panic recovery of the application (note: this is not for the HTTP/gRPC servers).
// So that even if the main function panics we can produce required logs for troubleshooting
var exitErr error

var ErrSigQuit = errors.New("received terminal signal")

func recoverer() {
	exitCode := 0
	var exitInfo any
	rec := recover()
	err, _ := rec.(error)
	if err != nil {
		exitCode = 1
		exitInfo = err
	} else if rec != nil {
		exitCode = 2
		exitInfo = rec
	} else if exitErr != nil {
		exitCode = 3
		exitInfo = exitErr
	}

	// exiting after receiving a quit signal can be considered a *clean/successful* exit
	if errors.Is(exitErr, ErrSigQuit) {
		exitCode = 0
	}

	// logging this because we have info logs saying "listening to" various port numbers
	// based on the server type (gRPC, HTTP etc.). But it's unclear *from the logs*
	// if the server is up and running, if it exits for any reason
	if exitCode == 0 {
		slog.Info("shutdown complete", "exitInfo", fmt.Sprintf("%+v", exitInfo))
	} else {
		slog.Error("shutdown complete", "exitcode", exitCode, "exitinfo", fmt.Sprintf("%+v", exitInfo))
	}

	os.Exit(exitCode)
}

func main() {
	defer recoverer()
	var (
		ctx                 = context.Background()
		fatalErr            = make(chan error, 1)
		shutdownGraceperiod = time.Minute
		probeInterval       = time.Second * 3
		httpPort            = flag.String("httpPort", "443", "Port to run HTTP server")
		pgHost              = flag.String("postgresHost", "localhost", "Postgres server address")
		pgPort              = flag.String("postgresPort", "5432", "Potgres server port")
		pgDriver            = flag.String("postgresDriver", "postgres", "Postgres driver")
		pgDb                = flag.String("postgresDb", "mydatabase", "Postgres database")
		pgUsername          = flag.String("postgresUsername", "postgres", "Postgres Username")
		pgPwd               = flag.String("postgresPassword", "postgres", "Postgres Password")
		debug               = flag.Bool("debug", false, "Enable debug logging")
		sslCert             = flag.String("sslCert", "server.crt", "SSL Cert")
		sslKey              = flag.String("sslKey", "server.key", "SSL Key")
	)

	flag.Parse()

	cfgs, err := configs.New(*httpPort, *pgHost, *pgPort, *pgDriver, *pgDb,
		*pgUsername, *pgPwd)
	if err != nil {
		panic(errors.Wrap(err))
	}

	logFile, err := os.OpenFile("zia-app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatal(err)
	}
	defer logFile.Close()

	lvl := new(slog.LevelVar)
	if *debug {
		lvl.Set(slog.LevelDebug)
	} else {
		lvl.Set(slog.LevelInfo)
	}

	logger := slog.New(slog.NewTextHandler(logFile, &slog.HandlerOptions{
		Level: lvl,
	}))
	slog.SetDefault(logger)

	postgresConfig := cfgs.Postgres
	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s",
		postgresConfig.Host, postgresConfig.Username, postgresConfig.Password,
		postgresConfig.StoreName, postgresConfig.Port)
	db, err := gorm.Open(gormPostgres.Open(dsn), &gorm.Config{})
	if err != nil {
		slog.Error("Failed to open DB connection", "error", err.Error())
		panic(errors.Wrap(err))
	}

	start(ctx, db, cfgs, logFile, *sslCert, *sslKey, fatalErr)

	defer shutdown(
		shutdownGraceperiod,
		probeInterval,
		db,
	)
	exitErr = <-fatalErr
}
