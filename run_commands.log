2025-09-03 21:55:12 | Running: golangci-lint run
2025-09-03 21:55:14 | ✅ Success: golangci-lint run
2025-09-03 21:55:15 | Running: go test ./...
2025-09-03 21:55:20 | ❌ Failed: go test ./... (exit code 1)
2025-09-03 21:55:20 | ----- Output Start -----
?   	ziaacademy-backend	[no test files]
?   	ziaacademy-backend/cmd/server/http	[no test files]
2025/09/03 21:55:17 INFO Setting up HTTP router skip_auth=false
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/09/03 21:55:17 INFO CORS middleware configured allowed_origins="[http://localhost:3000 http://localhost:3001 https://localhost:3000 https://localhost:3001 https://zac-admin-seven.vercel.app]" allowed_methods="[GET POST PUT DELETE OPTIONS PATCH]" allow_credentials=true
2025/09/03 21:55:17 INFO Configuring public routes
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (5 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (5 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (5 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (5 handlers)
[GIN-debug] POST   /api/webhook/razorpay     --> ziaacademy-backend/cmd/server/http.(*Handlers).RazorpayWebhookHandler-fm (5 handlers)
2025/09/03 21:55:17 INFO Configuring protected routes auth_enabled=true
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (6 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (6 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (6 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (6 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (6 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (6 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (6 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (6 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (6 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (6 handlers)
[GIN-debug] POST   /api/institutions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateInstitution-fm (6 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (6 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (6 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (6 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (6 handlers)
[GIN-debug] GET    /api/institutions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetInstitutions-fm (6 handlers)
[GIN-debug] PUT    /api/institutions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateInstitution-fm (6 handlers)
[GIN-debug] DELETE /api/institutions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).DeleteInstitution-fm (6 handlers)
[GIN-debug] GET    /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudents-fm (6 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (6 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (6 handlers)
[GIN-debug] GET    /api/courses/:id          --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourseByID-fm (6 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (6 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (6 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (6 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (6 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (6 handlers)
[GIN-debug] GET    /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAdminUsers-fm (6 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (6 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (6 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (6 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (6 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (6 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (6 handlers)
[GIN-debug] GET    /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestQuestions-fm (6 handlers)
[GIN-debug] PUT    /api/tests/:test_id/active --> ziaacademy-backend/cmd/server/http.(*Handlers).ToggleTestActiveStatus-fm (6 handlers)
[GIN-debug] PUT    /api/tests/:test_id/results-disclosure --> ziaacademy-backend/cmd/server/http.(*Handlers).ToggleTestResultsDisclosure-fm (6 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (6 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (6 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (6 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (6 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (6 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (6 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (6 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (6 handlers)
[GIN-debug] PUT    /api/video-progress       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateVideoProgress-fm (6 handlers)
[GIN-debug] PUT    /api/study-material-progress --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateStudyMaterialProgress-fm (6 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (6 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (6 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (6 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (6 handlers)
[GIN-debug] POST   /api/orders               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateRazorpayOrder-fm (6 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
2025/09/03 21:55:17 INFO HTTP router setup completed swagger_enabled=true auth_middleware_enabled=true
2025/09/03 21:55:17 INFO Setting up HTTP router skip_auth=true
[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

2025/09/03 21:55:17 INFO CORS middleware configured allowed_origins="[http://localhost:3000 http://localhost:3001 https://localhost:3000 https://localhost:3001 https://zac-admin-seven.vercel.app]" allowed_methods="[GET POST PUT DELETE OPTIONS PATCH]" allow_credentials=true
2025/09/03 21:55:17 INFO Configuring public routes
[GIN-debug] POST   /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateStudent-fm (5 handlers)
[GIN-debug] POST   /api/students/send-verification-code --> ziaacademy-backend/cmd/server/http.(*Handlers).SendVerificationCode-fm (5 handlers)
[GIN-debug] POST   /api/students/verify-code --> ziaacademy-backend/cmd/server/http.(*Handlers).VerifyCode-fm (5 handlers)
[GIN-debug] POST   /api/login                --> ziaacademy-backend/cmd/server/http.(*Handlers).Login-fm (5 handlers)
[GIN-debug] POST   /api/webhook/razorpay     --> ziaacademy-backend/cmd/server/http.(*Handlers).RazorpayWebhookHandler-fm (5 handlers)
2025/09/03 21:55:17 INFO Configuring protected routes auth_enabled=false
[GIN-debug] POST   /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateCourse-fm (5 handlers)
[GIN-debug] POST   /api/courses/:course_id/tests/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).AssociateTestWithCourse-fm (5 handlers)
[GIN-debug] POST   /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSubject-fm (5 handlers)
[GIN-debug] POST   /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateChapter-fm (5 handlers)
[GIN-debug] POST   /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTopic-fm (5 handlers)
[GIN-debug] POST   /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateQuestion-fm (5 handlers)
[GIN-debug] POST   /api/videos               --> ziaacademy-backend/cmd/server/http.(*Handlers).AddVideo-fm (5 handlers)
[GIN-debug] POST   /api/studymaterials       --> ziaacademy-backend/cmd/server/http.(*Handlers).AddStudyMaterial-fm (5 handlers)
[GIN-debug] POST   /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateFormulaCards-fm (5 handlers)
[GIN-debug] POST   /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).CreatePreviousYearPapers-fm (5 handlers)
[GIN-debug] POST   /api/institutions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateInstitution-fm (5 handlers)
[GIN-debug] GET    /api/subjects             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSubjects-fm (5 handlers)
[GIN-debug] GET    /api/chapters             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetChapters-fm (5 handlers)
[GIN-debug] GET    /api/topics               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTopics-fm (5 handlers)
[GIN-debug] GET    /api/formula-cards        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetFormulaCards-fm (5 handlers)
[GIN-debug] GET    /api/institutions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetInstitutions-fm (5 handlers)
[GIN-debug] PUT    /api/institutions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateInstitution-fm (5 handlers)
[GIN-debug] DELETE /api/institutions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).DeleteInstitution-fm (5 handlers)
[GIN-debug] GET    /api/students             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudents-fm (5 handlers)
[GIN-debug] GET    /api/previous-year-papers --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAllPreviousYearPapersOrganizedByExamType-fm (5 handlers)
[GIN-debug] GET    /api/courses              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourses-fm (5 handlers)
[GIN-debug] GET    /api/courses/:id          --> ziaacademy-backend/cmd/server/http.(*Handlers).GetCourseByID-fm (5 handlers)
[GIN-debug] GET    /api/content              --> ziaacademy-backend/cmd/server/http.(*Handlers).GetContent-fm (5 handlers)
[GIN-debug] POST   /api/users/password       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdatePassword-fm (5 handlers)
[GIN-debug] GET    /api/questions            --> ziaacademy-backend/cmd/server/http.(*Handlers).GetQuestions-fm (5 handlers)
[GIN-debug] POST   /api/enroll/:course_id    --> ziaacademy-backend/cmd/server/http.(*Handlers).EnrollInCourse-fm (5 handlers)
[GIN-debug] POST   /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateAdmin-fm (5 handlers)
[GIN-debug] GET    /api/admins               --> ziaacademy-backend/cmd/server/http.(*Handlers).GetAdminUsers-fm (5 handlers)
[GIN-debug] POST   /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateSectionType-fm (5 handlers)
[GIN-debug] GET    /api/section-types        --> ziaacademy-backend/cmd/server/http.(*Handlers).GetSectionTypes-fm (5 handlers)
[GIN-debug] POST   /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTestType-fm (5 handlers)
[GIN-debug] GET    /api/test-types           --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestTypes-fm (5 handlers)
[GIN-debug] POST   /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTest-fm (5 handlers)
[GIN-debug] GET    /api/tests                --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTests-fm (5 handlers)
[GIN-debug] GET    /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestQuestions-fm (5 handlers)
[GIN-debug] PUT    /api/tests/:test_id/active --> ziaacademy-backend/cmd/server/http.(*Handlers).ToggleTestActiveStatus-fm (5 handlers)
[GIN-debug] PUT    /api/tests/:test_id/results-disclosure --> ziaacademy-backend/cmd/server/http.(*Handlers).ToggleTestResultsDisclosure-fm (5 handlers)
[GIN-debug] POST   /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).AddQuestionsToTest-fm (5 handlers)
[GIN-debug] DELETE /api/tests/:test_id/questions --> ziaacademy-backend/cmd/server/http.(*Handlers).RemoveQuestionsFromTest-fm (5 handlers)
[GIN-debug] POST   /api/test-responses       --> ziaacademy-backend/cmd/server/http.(*Handlers).RecordTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetStudentTestResponses-fm (5 handlers)
[GIN-debug] GET    /api/test-responses/rankings/:test_id --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTestRankings-fm (5 handlers)
[GIN-debug] POST   /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).AddComment-fm (5 handlers)
[GIN-debug] POST   /api/responses            --> ziaacademy-backend/cmd/server/http.(*Handlers).AddResponse-fm (5 handlers)
[GIN-debug] GET    /api/comments             --> ziaacademy-backend/cmd/server/http.(*Handlers).GetComments-fm (5 handlers)
[GIN-debug] PUT    /api/video-progress       --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateVideoProgress-fm (5 handlers)
[GIN-debug] PUT    /api/study-material-progress --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateStudyMaterialProgress-fm (5 handlers)
[GIN-debug] POST   /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateTransaction-fm (5 handlers)
[GIN-debug] GET    /api/transactions         --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactions-fm (5 handlers)
[GIN-debug] GET    /api/transactions/:id     --> ziaacademy-backend/cmd/server/http.(*Handlers).GetTransactionByID-fm (5 handlers)
[GIN-debug] PUT    /api/transactions/:id/status --> ziaacademy-backend/cmd/server/http.(*Handlers).UpdateTransactionStatus-fm (5 handlers)
[GIN-debug] POST   /api/orders               --> ziaacademy-backend/cmd/server/http.(*Handlers).CreateRazorpayOrder-fm (5 handlers)
[GIN-debug] GET    /swagger/*any             --> github.com/swaggo/gin-swagger.CustomWrapHandler.func1 (5 handlers)
2025/09/03 21:55:17 INFO HTTP router setup completed swagger_enabled=true auth_middleware_enabled=false

2025/09/03 21:55:17 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/integration_test.go:31 [35;1mERROR: update or delete on table "chapters" violates foreign key constraint "topics_chapter_id_fkey" on table "topics" (SQLSTATE 23503)
[0m[33m[8.371ms] [34;1m[rows:0][0m DELETE FROM chapters WHERE name LIKE 'Test %'

2025/09/03 21:55:17 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/integration_test.go:34 [35;1mERROR: column "subject_id" does not exist (SQLSTATE 42703)
[0m[33m[2.460ms] [34;1m[rows:0][0m DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name LIKE 'Test %' OR name = 'Mathematics' OR name = 'Chemistry 12th' OR name = 'Physics Test' OR name LIKE '% Test Formula')

2025/09/03 21:55:17 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/integration_test.go:36 [35;1mERROR: update or delete on table "chapters" violates foreign key constraint "topics_chapter_id_fkey" on table "topics" (SQLSTATE 23503)
[0m[33m[22.769ms] [34;1m[rows:0][0m DELETE FROM subjects WHERE name LIKE 'Test %' OR name = 'Mathematics' OR name = 'Chemistry 12th' OR name = 'Physics Test' OR name LIKE '% Test Formula'
2025/09/03 21:55:17 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating admin user email=<EMAIL> full_name="Test Admin"
2025/09/03 21:55:17 INFO Admin user created successfully admin_id=2640 email=<EMAIL> full_name="Test Admin" duration_ms=65
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=70 response_size=201
[GIN] 2025/09/03 - 21:55:17 | 201 |     70.4809ms |                 | POST     "/api/admins"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating admin user email=<EMAIL> full_name="Test Admin 1"
2025/09/03 21:55:17 INFO Admin user created successfully admin_id=2641 email=<EMAIL> full_name="Test Admin 1" duration_ms=75
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=76 response_size=203
[GIN] 2025/09/03 - 21:55:17 | 201 |     76.3248ms |                 | POST     "/api/admins"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating admin user email=<EMAIL> full_name="Test Admin 2"
2025/09/03 21:55:17 INFO Admin user created successfully admin_id=2642 email=<EMAIL> full_name="Test Admin 2" duration_ms=71
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=71 response_size=203
[GIN] 2025/09/03 - 21:55:17 | 201 |     74.1274ms |                 | POST     "/api/admins"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:17 INFO GetAdminUsers request started client_ip=""
2025/09/03 21:55:17 INFO Retrieving all admin users
2025/09/03 21:55:17 INFO Admin users retrieved successfully count=3 duration_ms=0
2025/09/03 21:55:17 INFO GetAdminUsers completed successfully user_id=2641 client_ip="" admin_count=3 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/admins client_ip="" status_code=200 duration_ms=0 response_size=395
[GIN] 2025/09/03 - 21:55:17 | 200 |         596µs |                 | GET      "/api/admins"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/comments client_ip="" user_agent=""
2025/09/03 21:55:17 INFO AddComment request started client_ip="" method=POST path=/api/comments
2025/09/03 21:55:17 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:17 WARN AddComment failed - token extraction error client_ip="" error="token contains an invalid number of segments" duration_ms=0
2025/09/03 21:55:17 WARN Request completed method=POST path=/api/comments client_ip="" status_code=400 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:17 | 400 |            0s |                 | POST     "/api/comments"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/responses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO AddResponse request started client_ip="" method=POST path=/api/responses
2025/09/03 21:55:17 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:17 WARN AddResponse failed - token extraction error client_ip="" error="token contains an invalid number of segments" duration_ms=0
2025/09/03 21:55:17 WARN Request completed method=POST path=/api/responses client_ip="" status_code=400 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:17 | 400 |            0s |                 | POST     "/api/responses"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/comments client_ip="" user_agent=""
2025/09/03 21:55:17 INFO GetComments request started client_ip="" method=GET path=/api/comments
2025/09/03 21:55:17 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:17 WARN GetComments failed - token extraction error client_ip="" error="token contains an invalid number of segments" duration_ms=0
2025/09/03 21:55:17 WARN Request completed method=GET path=/api/comments client_ip="" status_code=400 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:17 | 400 |            0s |                 | GET      "/api/comments?video_id=1"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/comments client_ip="" user_agent=""
2025/09/03 21:55:17 INFO AddComment request started client_ip="" method=POST path=/api/comments
2025/09/03 21:55:17 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:17 WARN AddComment failed - token extraction error client_ip="" error="token contains an invalid number of segments" duration_ms=0
2025/09/03 21:55:17 WARN Request completed method=POST path=/api/comments client_ip="" status_code=400 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:17 | 400 |            0s |                 | POST     "/api/comments"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/comments client_ip="" user_agent=""
2025/09/03 21:55:17 INFO AddComment request started client_ip="" method=POST path=/api/comments
2025/09/03 21:55:17 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:17 WARN AddComment failed - token extraction error client_ip="" error="token contains an invalid number of segments" duration_ms=0
2025/09/03 21:55:17 WARN Request completed method=POST path=/api/comments client_ip="" status_code=400 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:17 | 400 |            0s |                 | POST     "/api/comments"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating student email=<EMAIL>
2025/09/03 21:55:17 INFO Student created successfully email=<EMAIL> student_id=1759 duration_ms=53
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=53 response_size=537
[GIN] 2025/09/03 - 21:55:17 | 200 |     53.0419ms |                 | POST     "/api/students"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:17 INFO GetContent request started client_ip="" user_id=2643 user_role=Student is_student=true student_id=1759 course_id=<nil>
2025/09/03 21:55:17 INFO Retrieving content organized by subjects with progress student_id=1759 course_id=<nil>
2025/09/03 21:55:17 INFO Content by subjects with progress retrieved successfully student_id=1759 course_id=<nil> subject_count=15 total_videos=15 total_pdfs=7 duration_ms=23
2025/09/03 21:55:17 INFO GetContent successful client_ip="" student_id=1759 course_id=<nil> subject_count=15 total_videos=15 total_materials=7 duration_ms=30
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/content client_ip="" status_code=200 duration_ms=31 response_size=7238
[GIN] 2025/09/03 - 21:55:17 | 200 |     31.1472ms |                 | GET      "/api/content"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:17 INFO GetContent request started client_ip="" user_id=2643 user_role=Student is_student=true student_id=1759 course_id=0xc00108a458
2025/09/03 21:55:17 INFO Retrieving content organized by subjects with progress student_id=1759 course_id=0xc00108a458
2025/09/03 21:55:17 INFO Content by subjects with progress retrieved successfully student_id=1759 course_id=0xc00108a458 subject_count=2 total_videos=2 total_pdfs=2 duration_ms=1
2025/09/03 21:55:17 INFO GetContent successful client_ip="" student_id=1759 course_id=0xc00108a458 subject_count=2 total_videos=2 total_materials=2 duration_ms=2
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/content client_ip="" status_code=200 duration_ms=2 response_size=1282
[GIN] 2025/09/03 - 21:55:17 | 200 |       2.088ms |                 | GET      "/api/content?course_id=2651"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:17 INFO GetContent request started client_ip="" user_id=2643 user_role=Student is_student=true student_id=1759 course_id=0xc00108ae88
2025/09/03 21:55:17 INFO Retrieving content organized by subjects with progress student_id=1759 course_id=0xc00108ae88
2025/09/03 21:55:17 INFO Content by subjects with progress retrieved successfully student_id=1759 course_id=0xc00108ae88 subject_count=0 total_videos=0 total_pdfs=0 duration_ms=1
2025/09/03 21:55:17 INFO GetContent successful client_ip="" student_id=1759 course_id=0xc00108ae88 subject_count=0 total_videos=0 total_materials=0 duration_ms=1
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/content client_ip="" status_code=200 duration_ms=1 response_size=109
[GIN] 2025/09/03 - 21:55:17 | 200 |      1.8137ms |                 | GET      "/api/content?course_id=99999"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:17 INFO GetContent request started client_ip="" user_id=2643 user_role=Student is_student=true student_id=1759 course_id=0xc00108b528
2025/09/03 21:55:17 INFO Retrieving content organized by subjects with progress student_id=1759 course_id=0xc00108b528
2025/09/03 21:55:17 INFO Content by subjects with progress retrieved successfully student_id=1759 course_id=0xc00108b528 subject_count=1 total_videos=1 total_pdfs=1 duration_ms=1
2025/09/03 21:55:17 INFO GetContent successful client_ip="" student_id=1759 course_id=0xc00108b528 subject_count=1 total_videos=1 total_materials=1 duration_ms=1
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/content client_ip="" status_code=200 duration_ms=1 response_size=685
[GIN] 2025/09/03 - 21:55:17 | 200 |      1.8413ms |                 | GET      "/api/content?course_id=2652"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:17 ERROR GetContent failed - invalid course_id parameter client_ip="" course_id=invalid error="strconv.Atoi: parsing \"invalid\": invalid syntax"
2025/09/03 21:55:17 WARN Request completed method=GET path=/api/content client_ip="" status_code=400 duration_ms=0 response_size=39
[GIN] 2025/09/03 - 21:55:17 | 400 |       504.8µs |                 | GET      "/api/content?course_id=invalid"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="IIT-JEE Physics Course" price=2999 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="IIT-JEE Physics Course" course_id=2653 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:17 | 200 |       505.5µs |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="NEET Biology Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="NEET Biology Course" course_id=2654 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:17 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 WARN Request completed method=POST path=/api/courses client_ip="" status_code=400 duration_ms=0 response_size=67
[GIN] 2025/09/03 - 21:55:17 | 400 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Free IIT-JEE Course" price=0 course_type=IIT-JEE is_free=true subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Free IIT-JEE Course" course_id=2655 course_type=IIT-JEE is_free=true subject_count=0 duration_ms=1
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=1 response_size=40
[GIN] 2025/09/03 - 21:55:17 | 200 |      1.1478ms |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Paid IIT-JEE Course" price=1999 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Paid IIT-JEE Course" course_id=2656 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:17 | 200 |       504.6µs |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Free NEET Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Free NEET Course" course_id=2657 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:17 | 200 |       515.3µs |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Paid NEET Course" price=2499 course_type=NEET is_free=false subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Paid NEET Course" course_id=2658 course_type=NEET is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:17 | 200 |       517.7µs |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Unenrolled Paid Course" price=3999 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Unenrolled Paid Course" course_id=2659 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:17 | 200 |       516.4µs |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating student email=<EMAIL>
2025/09/03 21:55:17 INFO Student created successfully email=<EMAIL> student_id=1760 duration_ms=59
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=67 response_size=495
[GIN] 2025/09/03 - 21:55:17 | 200 |     67.6366ms |                 | POST     "/api/students"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Courses retrieved successfully user_id=2644 user_role=Student total_courses=5 filtered_courses_returned=4 free_courses_count=2 enrolled_paid_courses_count=2 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/courses client_ip="" status_code=200 duration_ms=1 response_size=943
[GIN] 2025/09/03 - 21:55:17 | 200 |      1.3458ms |                 | GET      "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating admin user email=<EMAIL> full_name="Test Admin"
2025/09/03 21:55:17 INFO Admin user created successfully admin_id=2645 email=<EMAIL> full_name="Test Admin" duration_ms=60
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=65 response_size=201
[GIN] 2025/09/03 - 21:55:17 | 201 |     65.9065ms |                 | POST     "/api/admins"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Non-student user detected, returning all courses user_id=2645 role=Admin total_courses=5
2025/09/03 21:55:17 INFO Courses retrieved successfully user_id=2645 user_role=Admin total_courses=5 filtered_courses_returned=5 free_courses_count=2 enrolled_paid_courses_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=1165
[GIN] 2025/09/03 - 21:55:17 | 200 |            0s |                 | GET      "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Test Free Course" price=0 course_type=IIT-JEE is_free=true subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Test Free Course" course_id=2660 course_type=IIT-JEE is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:17 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Test Enrolled Paid Course" price=1999 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Test Enrolled Paid Course" course_id=2661 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=3
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=3 response_size=46
[GIN] 2025/09/03 - 21:55:17 | 200 |      3.9885ms |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating course name="Test Unenrolled Paid Course" price=2999 course_type=NEET is_free=false subject_count=0
2025/09/03 21:55:17 INFO Course created successfully name="Test Unenrolled Paid Course" course_id=2662 course_type=NEET is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=48
[GIN] 2025/09/03 - 21:55:17 | 200 |       541.5µs |                 | POST     "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating student email=<EMAIL>
2025/09/03 21:55:17 INFO Student created successfully email=<EMAIL> student_id=1761 duration_ms=56
2025/09/03 21:55:17 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=56 response_size=510
[GIN] 2025/09/03 - 21:55:17 | 200 |     56.2736ms |                 | POST     "/api/students"
2025/09/03 21:55:17 INFO Request started method=GET path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Courses retrieved successfully user_id=2646 user_role=Student total_courses=3 filtered_courses_returned=2 free_courses_count=1 enrolled_paid_courses_count=1 duration_ms=1
2025/09/03 21:55:17 INFO Request completed method=GET path=/api/courses client_ip="" status_code=200 duration_ms=1 response_size=527
[GIN] 2025/09/03 - 21:55:17 | 200 |       1.017ms |                 | GET      "/api/courses"
2025/09/03 21:55:17 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:17 INFO Creating admin user email=<EMAIL> full_name="Filter Admin"
2025/09/03 21:55:18 INFO Admin user created successfully admin_id=2647 email=<EMAIL> full_name="Filter Admin" duration_ms=71
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=73 response_size=203
[GIN] 2025/09/03 - 21:55:18 | 201 |     73.6834ms |                 | POST     "/api/admins"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Non-student user detected, returning all courses user_id=2647 role=Admin total_courses=3
2025/09/03 21:55:18 INFO Courses retrieved successfully user_id=2647 user_role=Admin total_courses=3 filtered_courses_returned=3 free_courses_count=1 enrolled_paid_courses_count=0 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=787
[GIN] 2025/09/03 - 21:55:18 | 200 |            0s |                 | GET      "/api/courses"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Test Physics" subject_display_name="Test Physics Subject"
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3033 subject_name="Test Physics" subject_display_name="Test Physics Subject" duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=2 response_size=33
[GIN] 2025/09/03 - 21:55:18 | 200 |      2.0203ms |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Test Chemistry" subject_display_name="Test Chemistry Subject"
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3034 subject_name="Test Chemistry" subject_display_name="Test Chemistry Subject" duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=35
[GIN] 2025/09/03 - 21:55:18 | 200 |       505.2µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Test Chapter 1" chapter_display_name="Test Chapter 1 Display" subject_name="Test Physics"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1326 chapter_name="Test Chapter 1" chapter_display_name="Test Chapter 1 Display" subject_name="Test Physics" subject_id=3033 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=1 response_size=35
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.0308ms |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Test Chapter 2" chapter_display_name="Test Chapter 2 Display" subject_name="Test Chemistry"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1327 chapter_name="Test Chapter 2" chapter_display_name="Test Chapter 2 Display" subject_name="Test Chemistry" subject_id=3034 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=1 response_size=35
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.6781ms |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/videos client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Adding video to chapter video_name="Test Video 1" video_display_name="Test Video 1 Display" chapter_name="Test Chapter 1"
2025/09/03 21:55:18 INFO Video added successfully video_id=259 video_name="Test Video 1" video_display_name="Test Video 1 Display" chapter_name="Test Chapter 1" chapter_id=1326 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/videos client_ip="" status_code=200 duration_ms=0 response_size=32
[GIN] 2025/09/03 - 21:55:18 | 200 |       505.8µs |                 | POST     "/api/videos"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/studymaterials client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Adding study material to chapter material_name="Test Material 1" material_display_name="Test Material 1 Display" chapter_name="Test Chapter 1"
2025/09/03 21:55:18 INFO Study material added successfully material_id=213 material_name="Test Material 1" material_display_name="Test Material 1 Display" chapter_name="Test Chapter 1" chapter_id=1326 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/studymaterials client_ip="" status_code=200 duration_ms=0 response_size=35
[GIN] 2025/09/03 - 21:55:18 | 200 |       508.8µs |                 | POST     "/api/studymaterials"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating course name="Test Course for Details" price=1999 course_type=IIT-JEE is_free=false subject_count=2
2025/09/03 21:55:18 INFO Course created successfully name="Test Course for Details" course_id=2663 course_type=IIT-JEE is_free=false subject_count=2 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=1 response_size=44
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.6591ms |                 | POST     "/api/courses"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/courses/2663 client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetCourseByID request started client_ip="" course_id=2663
2025/09/03 21:55:18 INFO Retrieving course details by ID course_id=2663
2025/09/03 21:55:18 INFO Course details retrieved successfully course_id=2663 course_name="Test Course for Details" subject_count=2 duration_ms=1
2025/09/03 21:55:18 INFO GetCourseByID successful client_ip="" course_id=2663 course_name="Test Course for Details" subject_count=2 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/courses/2663 client_ip="" status_code=200 duration_ms=1 response_size=1116
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.1484ms |                 | GET      "/api/courses/2663"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/courses/99999 client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetCourseByID request started client_ip="" course_id=99999
2025/09/03 21:55:18 INFO Retrieving course details by ID course_id=99999

2025/09/03 21:55:18 [31;1mD:/go/src/ziaacademy-backend/db/courses.go:361 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "courses" WHERE "courses"."id" = 99999 AND "courses"."deleted_at" IS NULL ORDER BY "courses"."id" LIMIT 1
2025/09/03 21:55:18 WARN Course not found course_id=99999 duration_ms=0
2025/09/03 21:55:18 WARN GetCourseByID failed - course not found client_ip="" course_id=99999 error="course with ID 99999 not found" duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=GET path=/api/courses/99999 client_ip="" status_code=404 duration_ms=0 response_size=42
[GIN] 2025/09/03 - 21:55:18 | 404 |            0s |                 | GET      "/api/courses/99999"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/courses/invalid client_ip="" user_agent=""
2025/09/03 21:55:18 WARN GetCourseByID failed - invalid course ID client_ip="" course_id_param=invalid error="strconv.ParseUint: parsing \"invalid\": invalid syntax" duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=GET path=/api/courses/invalid client_ip="" status_code=400 duration_ms=0 response_size=29
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | GET      "/api/courses/invalid"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Physics Test Formula" subject_display_name=Physics
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3035 subject_name="Physics Test Formula" subject_display_name=Physics duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:18 | 200 |       508.4µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Mechanics Test Formula" chapter_display_name=Mechanics subject_name="Physics Test Formula"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1328 chapter_name="Mechanics Test Formula" chapter_display_name=Mechanics subject_name="Physics Test Formula" subject_id=3035 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=1 response_size=43
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.0365ms |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Laws of Motion Test Formula" chapter_name="Mechanics Test Formula"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Laws of Motion Test Formula" topic_id=1052 chapter_name="Mechanics Test Formula" chapter_id=1328 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=48
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.6202ms |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Physics Test Formula" chapter_name="Mechanics Test Formula" topic_name="Laws of Motion Test Formula" card_count=2
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Physics Test Formula" chapter_name="Mechanics Test Formula" topic_name="Laws of Motion Test Formula" topic_id=1052 card_count=2 duration_ms=2
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Physics Test Formula" chapter_name="Mechanics Test Formula" topic_name="Laws of Motion Test Formula" card_count=2 duration_ms=2
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=2 response_size=86
[GIN] 2025/09/03 - 21:55:18 | 200 |      2.4562ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Mathematics Test Formula" subject_display_name=Mathematics
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3036 subject_name="Mathematics Test Formula" subject_display_name=Mathematics duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=45
[GIN] 2025/09/03 - 21:55:18 | 200 |       578.8µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Algebra Test Formula" chapter_display_name=Algebra subject_name="Mathematics Test Formula"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1329 chapter_name="Algebra Test Formula" chapter_display_name=Algebra subject_name="Mathematics Test Formula" subject_id=3036 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:18 | 200 |         505µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Quadratic Equations Test Formula" chapter_name="Algebra Test Formula"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Quadratic Equations Test Formula" topic_id=1053 chapter_name="Algebra Test Formula" chapter_id=1329 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=53
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.1056ms |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Mathematics Test Formula" chapter_name="Algebra Test Formula" topic_name="Quadratic Equations Test Formula" card_count=2
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Mathematics Test Formula" chapter_name="Algebra Test Formula" topic_name="Quadratic Equations Test Formula" topic_id=1053 card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Mathematics Test Formula" chapter_name="Algebra Test Formula" topic_name="Quadratic Equations Test Formula" card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=1 response_size=82
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.6675ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Geometry Test Formula" chapter_display_name=Geometry subject_name="Mathematics Test Formula"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1330 chapter_name="Geometry Test Formula" chapter_display_name=Geometry subject_name="Mathematics Test Formula" subject_id=3036 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=42
[GIN] 2025/09/03 - 21:55:18 | 200 |       506.1µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Triangles Test Formula" chapter_name="Geometry Test Formula"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Triangles Test Formula" topic_id=1054 chapter_name="Geometry Test Formula" chapter_id=1330 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:18 | 200 |       536.3µs |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Mathematics Test Formula" chapter_name="Geometry Test Formula" topic_name="Triangles Test Formula" card_count=1
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Mathematics Test Formula" chapter_name="Geometry Test Formula" topic_name="Triangles Test Formula" topic_id=1054 card_count=1 duration_ms=1
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Mathematics Test Formula" chapter_name="Geometry Test Formula" topic_name="Triangles Test Formula" card_count=1 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=1 response_size=42
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.2005ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Physics Test Formula" subject_display_name=Physics
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3037 subject_name="Physics Test Formula" subject_display_name=Physics duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:18 | 200 |       505.3µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Mechanics Test Formula" chapter_display_name=Mechanics subject_name="Physics Test Formula"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1331 chapter_name="Mechanics Test Formula" chapter_display_name=Mechanics subject_name="Physics Test Formula" subject_id=3037 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:18 | 200 |       513.3µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Laws of Motion Test Formula" chapter_name="Mechanics Test Formula"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Laws of Motion Test Formula" topic_id=1055 chapter_name="Mechanics Test Formula" chapter_id=1331 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=48
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.0363ms |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Physics Test Formula" chapter_name="Mechanics Test Formula" topic_name="Laws of Motion Test Formula" card_count=2
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Physics Test Formula" chapter_name="Mechanics Test Formula" topic_name="Laws of Motion Test Formula" topic_id=1055 card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Physics Test Formula" chapter_name="Mechanics Test Formula" topic_name="Laws of Motion Test Formula" card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=1 response_size=86
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.5791ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO GetFormulaCards successful for organized cards client_ip="" subject_count=2 total_card_count=5 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/formula-cards client_ip="" status_code=200 duration_ms=0 response_size=1663
[GIN] 2025/09/03 - 21:55:18 | 200 |       651.4µs |                 | GET      "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO GetFormulaCards successful for organized cards client_ip="" subject_count=2 total_card_count=5 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/formula-cards client_ip="" status_code=200 duration_ms=0 response_size=1663
[GIN] 2025/09/03 - 21:55:18 | 200 |       450.4µs |                 | GET      "/api/formula-cards?subject_name=Mathematics+Test+Formula"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name=NonExistentSubject chapter_name=NonExistentChapter topic_name=NonExistentTopic card_count=1

2025/09/03 21:55:18 [31;1mD:/go/src/ziaacademy-backend/db/formula_cards.go:32 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT "topics"."id","topics"."created_at","topics"."updated_at","topics"."deleted_at","topics"."name","topics"."chapter_id" FROM "topics" JOIN chapters ON topics.chapter_id = chapters.id JOIN subjects ON chapters.subject_id = subjects.id WHERE (topics.name = 'NonExistentTopic' AND chapters.name = 'NonExistentChapter' AND subjects.name = 'NonExistentSubject') AND "topics"."deleted_at" IS NULL ORDER BY "topics"."id" LIMIT 1
2025/09/03 21:55:18 ERROR Topic not found for formula cards creation subject_name=NonExistentSubject chapter_name=NonExistentChapter topic_name=NonExistentTopic error="record not found" duration_ms=0
2025/09/03 21:55:18 ERROR CreateFormulaCards failed - database error client_ip="" subject_name=NonExistentSubject chapter_name=NonExistentChapter topic_name=NonExistentTopic card_count=1 error="topic 'NonExistentTopic' in chapter 'NonExistentChapter' of subject 'NonExistentSubject' not found: record not found" duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/formula-cards client_ip="" status_code=404 duration_ms=0 response_size=48
[GIN] 2025/09/03 - 21:55:18 | 404 |       536.2µs |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Biology Test Formula" subject_display_name=Biology
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3038 subject_name="Biology Test Formula" subject_display_name=Biology duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:18 | 200 |         520µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Cell Biology Test Formula" chapter_display_name="Cell Biology" subject_name="Biology Test Formula"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1332 chapter_name="Cell Biology Test Formula" chapter_display_name="Cell Biology" subject_name="Biology Test Formula" subject_id=3038 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=46
[GIN] 2025/09/03 - 21:55:18 | 200 |       516.3µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Cell Structure Test Formula" chapter_name="Cell Biology Test Formula"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Cell Structure Test Formula" topic_id=1056 chapter_name="Cell Biology Test Formula" chapter_id=1332 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=48
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.0353ms |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO GetFormulaCards successful for organized cards client_ip="" subject_count=0 total_card_count=0 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/formula-cards client_ip="" status_code=200 duration_ms=0 response_size=33
[GIN] 2025/09/03 - 21:55:18 | 200 |            0s |                 | GET      "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Physics Organized Test" subject_display_name="Physics Organized Test"
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3039 subject_name="Physics Organized Test" subject_display_name="Physics Organized Test" duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:18 | 200 |       514.6µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Mechanics Organized Test" chapter_display_name="Mechanics Organized Test" subject_name="Physics Organized Test"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1333 chapter_name="Mechanics Organized Test" chapter_display_name="Mechanics Organized Test" subject_name="Physics Organized Test" subject_id=3039 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=45
[GIN] 2025/09/03 - 21:55:18 | 200 |       530.2µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Laws of Motion Organized Test" chapter_name="Mechanics Organized Test"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Laws of Motion Organized Test" topic_id=1057 chapter_name="Mechanics Organized Test" chapter_id=1333 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=50
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.1226ms |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Physics Organized Test" chapter_name="Mechanics Organized Test" topic_name="Laws of Motion Organized Test" card_count=2
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Physics Organized Test" chapter_name="Mechanics Organized Test" topic_name="Laws of Motion Organized Test" topic_id=1057 card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Physics Organized Test" chapter_name="Mechanics Organized Test" topic_name="Laws of Motion Organized Test" card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=1 response_size=77
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.6243ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Chemistry Organized Test" subject_display_name="Chemistry Organized Test"
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3040 subject_name="Chemistry Organized Test" subject_display_name="Chemistry Organized Test" duration_ms=4
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=4 response_size=45
[GIN] 2025/09/03 - 21:55:18 | 200 |      4.9736ms |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Atomic Structure Organized Test" chapter_display_name="Atomic Structure Organized Test" subject_name="Chemistry Organized Test"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1334 chapter_name="Atomic Structure Organized Test" chapter_display_name="Atomic Structure Organized Test" subject_name="Chemistry Organized Test" subject_id=3040 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=52
[GIN] 2025/09/03 - 21:55:18 | 200 |       550.5µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Periodic Table Organized Test" chapter_name="Atomic Structure Organized Test"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Periodic Table Organized Test" topic_id=1058 chapter_name="Atomic Structure Organized Test" chapter_id=1334 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=50
[GIN] 2025/09/03 - 21:55:18 | 200 |       509.9µs |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Chemistry Organized Test" chapter_name="Atomic Structure Organized Test" topic_name="Periodic Table Organized Test" card_count=1
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Chemistry Organized Test" chapter_name="Atomic Structure Organized Test" topic_name="Periodic Table Organized Test" topic_id=1058 card_count=1 duration_ms=0
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Chemistry Organized Test" chapter_name="Atomic Structure Organized Test" topic_name="Periodic Table Organized Test" card_count=1 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=1 response_size=37
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.0465ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating subject subject_name="Math Organized Test" subject_display_name="Math Organized Test"
2025/09/03 21:55:18 INFO Subject created successfully subject_id=3041 subject_name="Math Organized Test" subject_display_name="Math Organized Test" duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:18 | 200 |       535.2µs |                 | POST     "/api/subjects"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating chapter chapter_name="Algebra Organized Test" chapter_display_name="Algebra Organized Test" subject_name="Math Organized Test"
2025/09/03 21:55:18 INFO Chapter created successfully chapter_id=1335 chapter_name="Algebra Organized Test" chapter_display_name="Algebra Organized Test" subject_name="Math Organized Test" subject_id=3041 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:18 | 200 |       572.8µs |                 | POST     "/api/chapters"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating topic topic_name="Equations Organized Test" chapter_name="Algebra Organized Test"
2025/09/03 21:55:18 INFO Topic created successfully topic_name="Equations Organized Test" topic_id=1059 chapter_name="Algebra Organized Test" chapter_id=1335 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=45
[GIN] 2025/09/03 - 21:55:18 | 200 |       504.6µs |                 | POST     "/api/topics"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreateFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO Creating formula cards subject_name="Math Organized Test" chapter_name="Algebra Organized Test" topic_name="Equations Organized Test" card_count=2
2025/09/03 21:55:18 INFO Formula cards created successfully subject_name="Math Organized Test" chapter_name="Algebra Organized Test" topic_name="Equations Organized Test" topic_id=1059 card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO CreateFormulaCards successful client_ip="" subject_name="Math Organized Test" chapter_name="Algebra Organized Test" topic_name="Equations Organized Test" card_count=2 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/formula-cards client_ip="" status_code=200 duration_ms=1 response_size=78
[GIN] 2025/09/03 - 21:55:18 | 200 |        1.11ms |                 | POST     "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO GetFormulaCards successful for organized cards client_ip="" subject_count=3 total_card_count=5 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/formula-cards client_ip="" status_code=200 duration_ms=0 response_size=1687
[GIN] 2025/09/03 - 21:55:18 | 200 |       577.5µs |                 | GET      "/api/formula-cards"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/formula-cards client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetFormulaCards request started client_ip=""
2025/09/03 21:55:18 INFO GetFormulaCards successful for organized cards client_ip="" subject_count=3 total_card_count=5 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/formula-cards client_ip="" status_code=200 duration_ms=0 response_size=1687
[GIN] 2025/09/03 - 21:55:18 | 200 |       728.6µs |                 | GET      "/api/formula-cards?subject_name=Physics+Organized+Test"

2025/09/03 21:55:18 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/formula_cards_test.go:542 [35;1mERROR: column "subject_id" does not exist (SQLSTATE 42703)
[0m[33m[0.518ms] [34;1m[rows:0][0m DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = 'Physics Organized Test')

2025/09/03 21:55:18 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/formula_cards_test.go:542 [35;1mERROR: column "subject_id" does not exist (SQLSTATE 42703)
[0m[33m[0.000ms] [34;1m[rows:0][0m DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = 'Chemistry Organized Test')

2025/09/03 21:55:18 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/formula_cards_test.go:542 [35;1mERROR: column "subject_id" does not exist (SQLSTATE 42703)
[0m[33m[0.000ms] [34;1m[rows:0][0m DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = 'Math Organized Test')
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating student email=<EMAIL>
2025/09/03 21:55:18 INFO Student created successfully email=<EMAIL> student_id=1762 duration_ms=69
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=71 response_size=487
[GIN] 2025/09/03 - 21:55:18 | 200 |     71.3577ms |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating student email=<EMAIL>
2025/09/03 21:55:18 INFO Student created successfully email=<EMAIL> student_id=1763 duration_ms=59
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=59 response_size=464
[GIN] 2025/09/03 - 21:55:18 | 200 |     59.7397ms |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=102
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=104
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=106
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=111
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating admin user email=<EMAIL> full_name="Admin User"
2025/09/03 21:55:18 INFO Admin user created successfully admin_id=2650 email=<EMAIL> full_name="Admin User" duration_ms=58
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=58 response_size=201
[GIN] 2025/09/03 - 21:55:18 | 201 |     58.0715ms |                 | POST     "/api/admins"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreatePreviousYearPapers request started client_ip=""
2025/09/03 21:55:18 INFO Creating previous year papers paper_count=2
2025/09/03 21:55:18 INFO Previous year papers created successfully paper_count=2 duration_ms=1
2025/09/03 21:55:18 INFO CreatePreviousYearPapers successful client_ip="" paper_count=2 duration_ms=2
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=2 response_size=71
[GIN] 2025/09/03 - 21:55:18 | 200 |      2.1193ms |                 | POST     "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreatePreviousYearPapers request started client_ip=""
2025/09/03 21:55:18 INFO Creating previous year papers paper_count=3
2025/09/03 21:55:18 INFO Previous year papers created successfully paper_count=3 duration_ms=0
2025/09/03 21:55:18 INFO CreatePreviousYearPapers successful client_ip="" paper_count=3 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=0 response_size=97
[GIN] 2025/09/03 - 21:55:18 | 200 |       523.6µs |                 | POST     "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreatePreviousYearPapers request started client_ip=""
2025/09/03 21:55:18 INFO Creating previous year papers paper_count=4
2025/09/03 21:55:18 INFO Previous year papers created successfully paper_count=4 duration_ms=0
2025/09/03 21:55:18 INFO CreatePreviousYearPapers successful client_ip="" paper_count=4 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=0 response_size=142
[GIN] 2025/09/03 - 21:55:18 | 200 |       525.4µs |                 | POST     "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType request started client_ip=""
2025/09/03 21:55:18 INFO All previous year papers retrieved and organized successfully total_papers=7 exam_types_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType successful client_ip="" exam_types_count=2 total_papers=7 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=0 response_size=1594
[GIN] 2025/09/03 - 21:55:18 | 200 |       517.8µs |                 | GET      "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType request started client_ip=""
2025/09/03 21:55:18 INFO All previous year papers retrieved and organized successfully total_papers=7 exam_types_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType successful client_ip="" exam_types_count=2 total_papers=7 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=0 response_size=1594
[GIN] 2025/09/03 - 21:55:18 | 200 |       524.5µs |                 | GET      "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType request started client_ip=""
2025/09/03 21:55:18 INFO All previous year papers retrieved and organized successfully total_papers=7 exam_types_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType successful client_ip="" exam_types_count=2 total_papers=7 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=0 response_size=1594
[GIN] 2025/09/03 - 21:55:18 | 200 |            0s |                 | GET      "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType request started client_ip=""
2025/09/03 21:55:18 INFO All previous year papers retrieved and organized successfully total_papers=7 exam_types_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetAllPreviousYearPapersOrganizedByExamType successful client_ip="" exam_types_count=2 total_papers=7 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/previous-year-papers client_ip="" status_code=200 duration_ms=0 response_size=1594
[GIN] 2025/09/03 - 21:55:18 | 200 |       284.1µs |                 | GET      "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreatePreviousYearPapers request started client_ip=""
2025/09/03 21:55:18 WARN CreatePreviousYearPapers failed - invalid exam type client_ip="" paper_index=0 exam_type=INVALID-EXAM duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/previous-year-papers client_ip="" status_code=400 duration_ms=0 response_size=65
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/previous-year-papers client_ip="" user_agent=""
2025/09/03 21:55:18 INFO CreatePreviousYearPapers request started client_ip=""
2025/09/03 21:55:18 WARN CreatePreviousYearPapers failed - invalid request body client_ip="" error="Key: 'PreviousYearPapersForCreate.Papers' Error:Field validation for 'Papers' failed on the 'min' tag" duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/previous-year-papers client_ip="" status_code=400 duration_ms=0 response_size=113
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/previous-year-papers"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating student email=<EMAIL>
2025/09/03 21:55:18 INFO Student created successfully email=<EMAIL> student_id=1764 duration_ms=58
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=58 response_size=489
[GIN] 2025/09/03 - 21:55:18 | 200 |     60.3893ms |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/students client_ip="" status_code=400 duration_ms=4 response_size=106
[GIN] 2025/09/03 - 21:55:18 | 400 |      4.0573ms |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 WARN Request completed method=POST path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=111
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream="" class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream="" class="" name="" email="" phone_number="" institution="" student_count=4 duration_ms=2
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream="" class="" name="" email="" phone_number="" institution="" student_count=4 duration_ms=2
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=2 response_size=1384
[GIN] 2025/09/03 - 21:55:18 | 200 |      2.9496ms |                 | GET      "/api/students"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream=IIT-JEE class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream=IIT-JEE class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream=IIT-JEE class="" name="" email="" phone_number="" institution="" student_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream=IIT-JEE class="" name="" email="" phone_number="" institution="" student_count=2 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=704
[GIN] 2025/09/03 - 21:55:18 | 200 |         516µs |                 | GET      "/api/students?stream=IIT-JEE"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream=NEET class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream=NEET class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream=NEET class="" name="" email="" phone_number="" institution="" student_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream=NEET class="" name="" email="" phone_number="" institution="" student_count=2 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=681
[GIN] 2025/09/03 - 21:55:18 | 200 |       512.5µs |                 | GET      "/api/students?stream=NEET"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class=11th name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream="" class=11th name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream="" class=11th name="" email="" phone_number="" institution="" student_count=2 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream="" class=11th name="" email="" phone_number="" institution="" student_count=2 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=694
[GIN] 2025/09/03 - 21:55:18 | 200 |            0s |                 | GET      "/api/students?class=11th"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class="" name=Alice email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream="" class="" name=Alice email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream="" class="" name=Alice email="" phone_number="" institution="" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream="" class="" name=Alice email="" phone_number="" institution="" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=349
[GIN] 2025/09/03 - 21:55:18 | 200 |       508.5µs |                 | GET      "/api/students?name=Alice"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class="" name="" email=student5_1756916718 phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream="" class="" name="" email=student5_1756916718 phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream="" class="" name="" email=student5_1756916718 phone_number="" institution="" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream="" class="" name="" email=student5_1756916718 phone_number="" institution="" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=349
[GIN] 2025/09/03 - 21:55:18 | 200 |       528.9µs |                 | GET      "/api/students?email=student5_1756916718"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class="" name="" email="" phone_number=987654321 institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream="" class="" name="" email="" phone_number=987654321 institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream="" class="" name="" email="" phone_number=987654321 institution="" student_count=4 duration_ms=1
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream="" class="" name="" email="" phone_number=987654321 institution="" student_count=4 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=1 response_size=1384
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.1054ms |                 | GET      "/api/students?phone_number=987654321"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class="" name="" email="" phone_number="" institution="ABC School"
2025/09/03 21:55:18 INFO Retrieving students with filters stream="" class="" name="" email="" phone_number="" institution="ABC School"
2025/09/03 21:55:18 INFO Students retrieved successfully stream="" class="" name="" email="" phone_number="" institution="ABC School" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream="" class="" name="" email="" phone_number="" institution="ABC School" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=349
[GIN] 2025/09/03 - 21:55:18 | 200 |            0s |                 | GET      "/api/students?institution=ABC School"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream=IIT-JEE class=11th name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Retrieving students with filters stream=IIT-JEE class=11th name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 INFO Students retrieved successfully stream=IIT-JEE class=11th name="" email="" phone_number="" institution="" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream=IIT-JEE class=11th name="" email="" phone_number="" institution="" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=349
[GIN] 2025/09/03 - 21:55:18 | 200 |       506.1µs |                 | GET      "/api/students?stream=IIT-JEE&class=11th"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream=IIT-JEE class="" name="" email="" phone_number="" institution="ABC School"
2025/09/03 21:55:18 INFO Retrieving students with filters stream=IIT-JEE class="" name="" email="" phone_number="" institution="ABC School"
2025/09/03 21:55:18 INFO Students retrieved successfully stream=IIT-JEE class="" name="" email="" phone_number="" institution="ABC School" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO GetStudents successful client_ip="" stream=IIT-JEE class="" name="" email="" phone_number="" institution="ABC School" student_count=1 duration_ms=0
2025/09/03 21:55:18 INFO Request completed method=GET path=/api/students client_ip="" status_code=200 duration_ms=0 response_size=349
[GIN] 2025/09/03 - 21:55:18 | 200 |       513.3µs |                 | GET      "/api/students?institution=ABC School&stream=IIT-JEE"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream=INVALID class="" name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 WARN GetStudents failed - invalid stream parameter client_ip="" stream=INVALID duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=GET path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=65
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | GET      "/api/students?stream=INVALID"
2025/09/03 21:55:18 INFO Request started method=GET path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO GetStudents request started client_ip="" stream="" class=INVALID name="" email="" phone_number="" institution=""
2025/09/03 21:55:18 WARN GetStudents failed - invalid class parameter client_ip="" class=INVALID duration_ms=0
2025/09/03 21:55:18 WARN Request completed method=GET path=/api/students client_ip="" status_code=400 duration_ms=0 response_size=83
[GIN] 2025/09/03 - 21:55:18 | 400 |            0s |                 | GET      "/api/students?class=INVALID"
2025/09/03 21:55:18 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:18 INFO Creating student email=<EMAIL>
2025/09/03 21:55:18 INFO Student created successfully email=<EMAIL> student_id=1769 duration_ms=55
2025/09/03 21:55:18 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=62 response_size=567
[GIN] 2025/09/03 - 21:55:18 | 200 |     62.7838ms |                 | POST     "/api/students"
2025/09/03 21:55:18 INFO Request started method=PUT path=/api/study-material-progress client_ip="" user_agent=""
2025/09/03 21:55:18 INFO UpdateStudyMaterialProgress request started client_ip="" student_id=1769 study_material_id=214
2025/09/03 21:55:18 INFO Updating study material progress student_id=1769 study_material_id=214

2025/09/03 21:55:18 [31;1mD:/go/src/ziaacademy-backend/db/study_material_progress.go:37 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "study_material_progresses" WHERE (student_id = 1769 AND study_material_id = 214) AND "study_material_progresses"."deleted_at" IS NULL ORDER BY "study_material_progresses"."id" LIMIT 1
2025/09/03 21:55:18 INFO Created new study material progress record student_id=1769 study_material_id=214 progress_id=15
2025/09/03 21:55:18 INFO UpdateStudyMaterialProgress successful student_id=1769 study_material_id=214 last_read_at=2025-09-03T21:55:18.516+05:30 duration_ms=1
2025/09/03 21:55:18 INFO UpdateStudyMaterialProgress successful client_ip="" student_id=1769 study_material_id=214 duration_ms=1
2025/09/03 21:55:18 INFO Request completed method=PUT path=/api/study-material-progress client_ip="" status_code=200 duration_ms=1 response_size=58
[GIN] 2025/09/03 - 21:55:18 | 200 |      1.7548ms |                 | PUT      "/api/study-material-progress"
2025/09/03 21:55:19 INFO Request started method=PUT path=/api/study-material-progress client_ip="" user_agent=""
2025/09/03 21:55:19 INFO UpdateStudyMaterialProgress request started client_ip="" student_id=1769 study_material_id=214
2025/09/03 21:55:19 INFO Updating study material progress student_id=1769 study_material_id=214
2025/09/03 21:55:19 INFO Updated existing study material progress record student_id=1769 study_material_id=214 progress_id=15
2025/09/03 21:55:19 INFO UpdateStudyMaterialProgress successful student_id=1769 study_material_id=214 last_read_at=2025-09-03T21:55:19.521+05:30 duration_ms=4
2025/09/03 21:55:19 INFO UpdateStudyMaterialProgress successful client_ip="" student_id=1769 study_material_id=214 duration_ms=5
2025/09/03 21:55:19 INFO Request completed method=PUT path=/api/study-material-progress client_ip="" status_code=200 duration_ms=5 response_size=58
[GIN] 2025/09/03 - 21:55:19 | 200 |      5.6513ms |                 | PUT      "/api/study-material-progress"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:19 INFO GetContent request started client_ip="" user_id=2656 user_role=Student is_student=true student_id=1769 course_id=<nil>
2025/09/03 21:55:19 INFO Retrieving content organized by subjects with progress student_id=1769 course_id=<nil>
2025/09/03 21:55:19 INFO Content by subjects with progress retrieved successfully student_id=1769 course_id=<nil> subject_count=13 total_videos=12 total_pdfs=5 duration_ms=26
2025/09/03 21:55:19 INFO GetContent successful client_ip="" student_id=1769 course_id=<nil> subject_count=13 total_videos=12 total_materials=5 duration_ms=27
2025/09/03 21:55:19 INFO Request completed method=GET path=/api/content client_ip="" status_code=200 duration_ms=27 response_size=6354
[GIN] 2025/09/03 - 21:55:19 | 200 |     27.3386ms |                 | GET      "/api/content"
2025/09/03 21:55:19 INFO Request started method=PUT path=/api/study-material-progress client_ip="" user_agent=""
2025/09/03 21:55:19 INFO UpdateStudyMaterialProgress request started client_ip="" student_id=1769 study_material_id=99999
2025/09/03 21:55:19 INFO Updating study material progress student_id=1769 study_material_id=99999

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/study_material_progress.go:23 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "study_materials" WHERE "study_materials"."id" = 99999 AND "study_materials"."deleted_at" IS NULL ORDER BY "study_materials"."id" LIMIT 1
2025/09/03 21:55:19 ERROR UpdateStudyMaterialProgress failed - study material not found student_id=1769 study_material_id=99999 error="record not found" duration_ms=0
2025/09/03 21:55:19 ERROR UpdateStudyMaterialProgress failed - database error client_ip="" student_id=1769 study_material_id=99999 error="study material with ID 99999 not found: record not found" duration_ms=0
2025/09/03 21:55:19 ERROR Request completed method=PUT path=/api/study-material-progress client_ip="" status_code=500 duration_ms=0 response_size=68
[GIN] 2025/09/03 - 21:55:19 | 500 |       513.9µs |                 | PUT      "/api/study-material-progress"
2025/09/03 21:55:19 INFO Request started method=PUT path=/api/study-material-progress client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR UpdateStudyMaterialProgress failed - invalid request body client_ip="" student_id=1769 error="Key: 'StudyMaterialProgressForUpdate.StudyMaterialID' Error:Field validation for 'StudyMaterialID' failed on the 'required' tag" duration_ms=0
2025/09/03 21:55:19 WARN Request completed method=PUT path=/api/study-material-progress client_ip="" status_code=400 duration_ms=0 response_size=139
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | PUT      "/api/study-material-progress"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name="Chemistry QQ" subject_display_name=Chemistry
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3043 subject_name="Chemistry QQ" subject_display_name=Chemistry duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=33
[GIN] 2025/09/03 - 21:55:19 | 200 |       504.7µs |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO All subjects retrieved successfully for admin user_id=2657 role=Admin subject_count=96 duration_ms=0
2025/09/03 21:55:19 INFO Subjects retrieved successfully for student user_id=2658 course_count=1 subject_count=1 duration_ms=0
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name=Mathematics subject_display_name=Mathematics
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3046 subject_name=Mathematics subject_display_name=Mathematics duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=32
[GIN] 2025/09/03 - 21:55:19 | 200 |            0s |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=2 response_size=33
[GIN] 2025/09/03 - 21:55:19 | 200 |      2.0329ms |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=33
[GIN] 2025/09/03 - 21:55:19 | 200 |       521.9µs |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=3 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 200 |      3.1501ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name="Physics Test Enhanced" subject_display_name="Physics Test Enhanced"
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3048 subject_name="Physics Test Enhanced" subject_display_name="Physics Test Enhanced" duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=42
[GIN] 2025/09/03 - 21:55:19 | 200 |       506.3µs |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name="Chemistry Test Enhanced" subject_display_name="Chemistry Test Enhanced"
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3049 subject_name="Chemistry Test Enhanced" subject_display_name="Chemistry Test Enhanced" duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=44
[GIN] 2025/09/03 - 21:55:19 | 200 |            0s |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name="Mathematics Test Enhanced" subject_display_name="Mathematics Test Enhanced"
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3050 subject_name="Mathematics Test Enhanced" subject_display_name="Mathematics Test Enhanced" duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=46
[GIN] 2025/09/03 - 21:55:19 | 200 |       533.8µs |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating topic topic_name="Newton's Laws Enhanced" chapter_name="Mechanics Enhanced"
2025/09/03 21:55:19 INFO Topic created successfully topic_name="Newton's Laws Enhanced" topic_id=1060 chapter_name="Mechanics Enhanced" chapter_id=1337 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:19 | 200 |       520.2µs |                 | POST     "/api/topics"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating topic topic_name="Hydrocarbons Enhanced" chapter_name="Organic Chemistry Enhanced"
2025/09/03 21:55:19 INFO Topic created successfully topic_name="Hydrocarbons Enhanced" topic_id=1061 chapter_name="Organic Chemistry Enhanced" chapter_id=1338 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=42
[GIN] 2025/09/03 - 21:55:19 | 200 |         514µs |                 | POST     "/api/topics"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating topic topic_name="Derivatives Enhanced" chapter_name="Calculus Enhanced"
2025/09/03 21:55:19 INFO Topic created successfully topic_name="Derivatives Enhanced" topic_id=1062 chapter_name="Calculus Enhanced" chapter_id=1339 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:19 | 200 |       515.8µs |                 | POST     "/api/topics"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="What is Newton's first law of motion?" topic_name="Newton's Laws Enhanced" difficulty_name=Medium
2025/09/03 21:55:19 INFO Question created successfully question_id=1235 question_text="What is Newton's first law of motion?" topic_name="Newton's Laws Enhanced" topic_id=1060 difficulty_name=Medium difficulty_id=345 duration_ms=3
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=3 response_size=58
[GIN] 2025/09/03 - 21:55:19 | 200 |      3.6562ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="Calculate the force required to accelerate a 10kg object at 5m/s²" topic_name="Newton's Laws Enhanced" difficulty_name=Medium
2025/09/03 21:55:19 INFO Question created successfully question_id=1236 question_text="Calculate the force required to accelerate a 10kg object at 5m/s²" topic_name="Newton's Laws Enhanced" topic_id=1060 difficulty_name=Medium difficulty_id=345 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=1 response_size=87
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6275ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="Which of the following is an alkane?" topic_name="Hydrocarbons Enhanced" difficulty_name=Medium
2025/09/03 21:55:19 INFO Question created successfully question_id=1237 question_text="Which of the following is an alkane?" topic_name="Hydrocarbons Enhanced" topic_id=1061 difficulty_name=Medium difficulty_id=345 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=1 response_size=57
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.9916ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="What is the general formula for alkenes?" topic_name="Hydrocarbons Enhanced" difficulty_name=Medium
2025/09/03 21:55:19 INFO Question created successfully question_id=1238 question_text="What is the general formula for alkenes?" topic_name="Hydrocarbons Enhanced" topic_id=1061 difficulty_name=Medium difficulty_id=345 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=1 response_size=61
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6246ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="What is the derivative of x²?" topic_name="Derivatives Enhanced" difficulty_name=Medium
2025/09/03 21:55:19 INFO Question created successfully question_id=1239 question_text="What is the derivative of x²?" topic_name="Derivatives Enhanced" topic_id=1062 difficulty_name=Medium difficulty_id=345 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=1 response_size=51
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6677ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="What is the unit of force in SI system?" topic_name="Newton's Laws Enhanced" difficulty_name=Medium
2025/09/03 21:55:19 INFO Question created successfully question_id=1240 question_text="What is the unit of force in SI system?" topic_name="Newton's Laws Enhanced" topic_id=1060 difficulty_name=Medium difficulty_id=345 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=1 response_size=60
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0888ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=45
[GIN] 2025/09/03 - 21:55:19 | 200 |       513.7µs |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=47
[GIN] 2025/09/03 - 21:55:19 | 200 |       522.3µs |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=1 response_size=49
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0477ms |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=43
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.7044ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=3 response_size=62
[GIN] 2025/09/03 - 21:55:19 | 200 |      3.5285ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/933/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests/933/questions client_ip="" status_code=200 duration_ms=2 response_size=50
[GIN] 2025/09/03 - 21:55:19 | 200 |      2.5618ms |                 | POST     "/api/tests/933/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/933/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests/933/questions client_ip="" status_code=200 duration_ms=2 response_size=50
[GIN] 2025/09/03 - 21:55:19 | 200 |      2.7407ms |                 | POST     "/api/tests/933/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/933/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests/933/questions client_ip="" status_code=200 duration_ms=1 response_size=50
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.2109ms |                 | POST     "/api/tests/933/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/933/questions client_ip="" user_agent=""

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:177 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "sections" WHERE (test_id = 933 AND name = 'nonexistent_section') AND "sections"."deleted_at" IS NULL ORDER BY "sections"."id" LIMIT 1
2025/09/03 21:55:19 ERROR Request completed method=POST path=/api/tests/933/questions client_ip="" status_code=500 duration_ms=0 response_size=71
[GIN] 2025/09/03 - 21:55:19 | 500 |       504.9µs |                 | POST     "/api/tests/933/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/933/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/tests/933/questions client_ip="" status_code=400 duration_ms=0 response_size=126
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/tests/933/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/933/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/tests/933/questions client_ip="" status_code=400 duration_ms=0 response_size=36
[GIN] 2025/09/03 - 21:55:19 | 400 |       536.1µs |                 | POST     "/api/tests/933/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests/99999/questions client_ip="" user_agent=""

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:155 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "tests" WHERE "tests"."id" = 99999 AND "tests"."deleted_at" IS NULL ORDER BY "tests"."id" LIMIT 1
2025/09/03 21:55:19 ERROR Request completed method=POST path=/api/tests/99999/questions client_ip="" status_code=500 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:19 | 500 |            0s |                 | POST     "/api/tests/99999/questions"
2025/09/03 21:55:19 INFO Request started method=PUT path=/api/tests/933/active client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Toggling test active status test_id=933
2025/09/03 21:55:19 INFO Test active status toggled successfully test_id=933 active=true duration_ms=0
2025/09/03 21:55:19 INFO ToggleTestActiveStatus successful client_ip="" test_id=933 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=PUT path=/api/tests/933/active client_ip="" status_code=200 duration_ms=0 response_size=53
[GIN] 2025/09/03 - 21:55:19 | 200 |       519.6µs |                 | PUT      "/api/tests/933/active"

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:550 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.000ms] [34;1m[rows:0][0m DELETE FROM "test_types" WHERE "test_types"."id" = 1133
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="Valid MCQ question?" topic_name="Test Topic Validation 1756916719625912900" difficulty_name="Test Difficulty Validation 1756916719625912900"
2025/09/03 21:55:19 INFO Question created successfully question_id=1241 question_text="Valid MCQ question?" topic_name="Test Topic Validation 1756916719625912900" topic_id=1063 difficulty_name="Test Difficulty Validation 1756916719625912900" difficulty_id=346 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=1 response_size=40
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0202ms |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating question question_text="Valid text question?" topic_name="Test Topic Validation 1756916719625912900" difficulty_name="Test Difficulty Validation 1756916719625912900"
2025/09/03 21:55:19 INFO Question created successfully question_id=1242 question_text="Valid text question?" topic_name="Test Topic Validation 1756916719625912900" topic_id=1063 difficulty_name="Test Difficulty Validation 1756916719625912900" difficulty_id=346 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/questions client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:19 | 200 |       513.5µs |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=100
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=71
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=71
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=60
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=50
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/questions client_ip="" user_agent=""
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/questions client_ip="" status_code=400 duration_ms=0 response_size=84
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | POST     "/api/questions"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating course name=TestCourse_1756916719631121300 price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:19 INFO Course created successfully name=TestCourse_1756916719631121300 course_id=2665 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=51
[GIN] 2025/09/03 - 21:55:19 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name=SubjectAssoc_1756916719631121300 subject_display_name=SubjectAssoc_1756916719631121300
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3052 subject_name=SubjectAssoc_1756916719631121300 subject_display_name=SubjectAssoc_1756916719631121300 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=53
[GIN] 2025/09/03 - 21:55:19 | 200 |       545.1µs |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=53
[GIN] 2025/09/03 - 21:55:19 | 200 |       516.7µs |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=54
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6689ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=52
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.7312ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses/2665/tests/934 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Associating test with course client_ip="" course_id=2665 test_id=934
2025/09/03 21:55:19 INFO Associating test with course course_id=2665 test_id=934
2025/09/03 21:55:19 INFO Test associated with course successfully course_id=2665 course_name=TestCourse_1756916719631121300 test_id=934 test_name=TestForAssoc_1756916719631121300 existing_tests_count=0 duration_ms=1
2025/09/03 21:55:19 INFO Test associated with course successfully client_ip="" course_id=2665 test_id=934 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/courses/2665/tests/934 client_ip="" status_code=200 duration_ms=1 response_size=85
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.7353ms |                 | POST     "/api/courses/2665/tests/934"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses/2665/tests/934 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Associating test with course client_ip="" course_id=2665 test_id=934
2025/09/03 21:55:19 INFO Associating test with course course_id=2665 test_id=934
2025/09/03 21:55:19 WARN Test already associated with course course_id=2665 test_id=934 course_name=TestCourse_1756916719631121300 test_name=TestForAssoc_1756916719631121300 duration_ms=0
2025/09/03 21:55:19 WARN AssociateTestWithCourse failed - already associated client_ip="" course_id=2665 test_id=934 error="test with ID 934 is already associated with course ID 2665" duration_ms=0
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/courses/2665/tests/934 client_ip="" status_code=409 duration_ms=0 response_size=70
[GIN] 2025/09/03 - 21:55:19 | 409 |       819.6µs |                 | POST     "/api/courses/2665/tests/934"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses/99999/tests/934 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Associating test with course client_ip="" course_id=99999 test_id=934
2025/09/03 21:55:19 INFO Associating test with course course_id=99999 test_id=934

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/courses.go:471 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "courses" WHERE "courses"."id" = 99999 AND "courses"."deleted_at" IS NULL ORDER BY "courses"."id" LIMIT 1
2025/09/03 21:55:19 WARN Course not found for association course_id=99999 test_id=934 duration_ms=0
2025/09/03 21:55:19 WARN AssociateTestWithCourse failed - resource not found client_ip="" course_id=99999 test_id=934 error="course with ID 99999 not found" duration_ms=0
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/courses/99999/tests/934 client_ip="" status_code=404 duration_ms=0 response_size=42
[GIN] 2025/09/03 - 21:55:19 | 404 |            0s |                 | POST     "/api/courses/99999/tests/934"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses/2665/tests/99999 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Associating test with course client_ip="" course_id=2665 test_id=99999
2025/09/03 21:55:19 INFO Associating test with course course_id=2665 test_id=99999

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/courses.go:493 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "tests" WHERE "tests"."id" = 99999 AND "tests"."deleted_at" IS NULL ORDER BY "tests"."id" LIMIT 1
2025/09/03 21:55:19 WARN Test not found for association course_id=2665 test_id=99999 duration_ms=0
2025/09/03 21:55:19 WARN AssociateTestWithCourse failed - resource not found client_ip="" course_id=2665 test_id=99999 error="test with ID 99999 not found" duration_ms=0
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/courses/2665/tests/99999 client_ip="" status_code=404 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:19 | 404 |       507.1µs |                 | POST     "/api/courses/2665/tests/99999"

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:917 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.505ms] [34;1m[rows:0][0m DELETE FROM "test_types" WHERE "test_types"."id" = 1134
2025/09/03 21:55:19 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating student email=<EMAIL>
2025/09/03 21:55:19 INFO Student created successfully email=<EMAIL> student_id=1771 duration_ms=58
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=61 response_size=558
[GIN] 2025/09/03 - 21:55:19 | 200 |     61.0519ms |                 | POST     "/api/students"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name=ResponseSubject_1756916719643449000 subject_display_name=ResponseSubject_1756916719643449000
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3053 subject_name=ResponseSubject_1756916719643449000 subject_display_name=ResponseSubject_1756916719643449000 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:19 | 200 |            0s |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0195ms |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=57
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0456ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=52
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0177ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-responses client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=POST path=/api/test-responses client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | POST     "/api/test-responses"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/935 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/935 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/935"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating student email=<EMAIL>
2025/09/03 21:55:19 INFO Student created successfully email=<EMAIL> student_id=1772 duration_ms=64
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=65 response_size=490
[GIN] 2025/09/03 - 21:55:19 | 200 |     65.7559ms |                 | POST     "/api/students"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/99999 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO GetStudentTestResponses request started user_id=2660 client_ip="" test_id=99999

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/test_responses.go:219 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "tests" WHERE "tests"."id" = 99999 AND "tests"."deleted_at" IS NULL ORDER BY "tests"."id" LIMIT 1
2025/09/03 21:55:19 ERROR Failed to retrieve test information student_id=1772 test_id=99999 error="record not found" duration_ms=0
2025/09/03 21:55:19 ERROR GetStudentTestResponses failed - database error user_id=2660 student_id=1772 client_ip="" test_id=99999 error="failed to retrieve test information: record not found" duration_ms=0
2025/09/03 21:55:19 ERROR Request completed method=GET path=/api/test-responses/99999 client_ip="" status_code=500 duration_ms=0 response_size=65
[GIN] 2025/09/03 - 21:55:19 | 500 |            0s |                 | GET      "/api/test-responses/99999"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name=RankingSubject_1756916719777499700 subject_display_name=RankingSubject_1756916719777499700
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3054 subject_name=RankingSubject_1756916719777499700 subject_display_name=RankingSubject_1756916719777499700 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=55
[GIN] 2025/09/03 - 21:55:19 | 200 |         523µs |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=55
[GIN] 2025/09/03 - 21:55:19 | 200 |       532.1µs |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=56
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.5643ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=51
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0348ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/936 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/936 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/936"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/99999 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/99999 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/99999"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/invalid client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/invalid client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/invalid"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/936 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/936 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/936?limit=10&offset=5"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/123 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/123 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/123"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/123 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/123 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/123?limit=50&offset=10"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/invalid client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/invalid client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/invalid"
[GIN-debug] redirecting request 301: /api/test-responses/rankings --> /api/test-responses/rankings
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/123 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/123 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/123?limit=-5&offset=-10"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/123 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/123 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/123?limit=1000&offset=5000"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/test-responses/rankings/123 client_ip="" user_agent=""
2025/09/03 21:55:19 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/test-responses/rankings/123 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:19 | 401 |            0s |                 | GET      "/api/test-responses/rankings/123?limit=abc&offset=xyz"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating admin user email=<EMAIL> full_name="GetTests Admin"
2025/09/03 21:55:19 INFO Admin user created successfully admin_id=2661 email=<EMAIL> full_name="GetTests Admin" duration_ms=55
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=55 response_size=205
[GIN] 2025/09/03 - 21:55:19 | 201 |     55.9683ms |                 | POST     "/api/admins"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating course name=GetTestsCourse_1756916719786065000 price=1000 course_type=NEET is_free=false subject_count=0
2025/09/03 21:55:19 INFO Course created successfully name=GetTestsCourse_1756916719786065000 course_id=2666 course_type=NEET is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=55
[GIN] 2025/09/03 - 21:55:19 | 200 |       505.2µs |                 | POST     "/api/courses"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name=GetTestsSubject_1756916719786065000 subject_display_name=GetTestsSubject_1756916719786065000
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3055 subject_name=GetTestsSubject_1756916719786065000 subject_display_name=GetTestsSubject_1756916719786065000 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:19 | 200 |       523.5µs |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=1 response_size=56
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0288ms |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=57
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6361ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=2 response_size=52
[GIN] 2025/09/03 - 21:55:19 | 200 |      2.5698ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses/2666/tests/937 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Associating test with course client_ip="" course_id=2666 test_id=937
2025/09/03 21:55:19 INFO Associating test with course course_id=2666 test_id=937
2025/09/03 21:55:19 INFO Test associated with course successfully course_id=2666 course_name=GetTestsCourse_1756916719786065000 test_id=937 test_name=GetTestsTest_1756916719786065000 existing_tests_count=0 duration_ms=1
2025/09/03 21:55:19 INFO Test associated with course successfully client_ip="" course_id=2666 test_id=937 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/courses/2666/tests/937 client_ip="" status_code=200 duration_ms=1 response_size=85
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6164ms |                 | POST     "/api/courses/2666/tests/937"
2025/09/03 21:55:19 INFO Request started method=PUT path=/api/tests/937/active client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Toggling test active status test_id=937
2025/09/03 21:55:19 INFO Test active status toggled successfully test_id=937 active=true duration_ms=0
2025/09/03 21:55:19 INFO ToggleTestActiveStatus successful client_ip="" test_id=937 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=PUT path=/api/tests/937/active client_ip="" status_code=200 duration_ms=0 response_size=53
[GIN] 2025/09/03 - 21:55:19 | 200 |         531µs |                 | PUT      "/api/tests/937/active"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO GetTests database operation completed user_id=2661 role=Admin test_count=6 active_filter=<nil> duration_ms=2
2025/09/03 21:55:19 INFO GetTests successful user_id=2661 client_ip="" active_filter="" test_count=6 duration_ms=2
2025/09/03 21:55:19 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=2 response_size=2920
[GIN] 2025/09/03 - 21:55:19 | 200 |      2.8564ms |                 | GET      "/api/tests"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO GetTests database operation completed user_id=2661 role=Admin test_count=5 active_filter=0xc00100a310 duration_ms=1
2025/09/03 21:55:19 INFO GetTests successful user_id=2661 client_ip="" active_filter=true test_count=5 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=2624
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.6918ms |                 | GET      "/api/tests?active=true"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 WARN GetTests failed - invalid active parameter user_id=2661 client_ip="" active_param=invalid duration_ms=0
2025/09/03 21:55:19 WARN Request completed method=GET path=/api/tests client_ip="" status_code=400 duration_ms=0 response_size=59
[GIN] 2025/09/03 - 21:55:19 | 400 |            0s |                 | GET      "/api/tests?active=invalid"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating student email=<EMAIL>
2025/09/03 21:55:19 INFO Student created successfully email=<EMAIL> student_id=1773 duration_ms=61
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=61 response_size=500
[GIN] 2025/09/03 - 21:55:19 | 200 |     61.9515ms |                 | POST     "/api/students"
2025/09/03 21:55:19 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO GetTests database operation completed user_id=2662 role=Student test_count=5 active_filter=<nil> duration_ms=1
2025/09/03 21:55:19 INFO GetTests successful user_id=2662 client_ip="" active_filter="" test_count=5 duration_ms=1
2025/09/03 21:55:19 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=2460
[GIN] 2025/09/03 - 21:55:19 | 200 |        1.04ms |                 | GET      "/api/tests"

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1508 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.000ms] [34;1m[rows:0][0m DELETE FROM "test_types" WHERE "test_types"."id" = 1137
--- FAIL: TestGetTests (0.15s)
    test_apis_test.go:1495: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1495
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "Mixed Test Type 1754843375"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+Mixed Test Type 1754843375
        	Test:       	TestGetTests
        	Messages:   	Student should only see ZSAT type tests when not enrolled
    test_apis_test.go:1495: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1495
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "Mixed Test Type 1754843415"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+Mixed Test Type 1754843415
        	Test:       	TestGetTests
        	Messages:   	Student should only see ZSAT type tests when not enrolled
    test_apis_test.go:1495: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1495
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "NEET Weekly"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+NEET Weekly
        	Test:       	TestGetTests
        	Messages:   	Student should only see ZSAT type tests when not enrolled
    test_apis_test.go:1495: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1495
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "Results Disclosure Test Type"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+Results Disclosure Test Type
        	Test:       	TestGetTests
        	Messages:   	Student should only see ZSAT type tests when not enrolled

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1542 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.000ms] [34;1m[rows:0][0m DELETE FROM test_types WHERE name IN ('ZSAT', 'RegularTestType_1756916719932014900')
2025/09/03 21:55:19 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating admin user email=<EMAIL> full_name="ZSAT Admin"
2025/09/03 21:55:19 INFO Admin user created successfully admin_id=2663 email=<EMAIL> full_name="ZSAT Admin" duration_ms=53
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=53 response_size=201
[GIN] 2025/09/03 - 21:55:19 | 201 |     53.2131ms |                 | POST     "/api/admins"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating course name=ZSATTestCourse_1756916719932014900 price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:19 INFO Course created successfully name=ZSATTestCourse_1756916719932014900 course_id=2667 course_type=NEET is_free=true subject_count=0 duration_ms=7
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=7 response_size=55
[GIN] 2025/09/03 - 21:55:19 | 200 |      7.5095ms |                 | POST     "/api/courses"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Creating subject subject_name=ZSATSubject_1756916719932014900 subject_display_name=ZSATSubject_1756916719932014900
2025/09/03 21:55:19 INFO Subject created successfully subject_id=3056 subject_name=ZSATSubject_1756916719932014900 subject_display_name=ZSATSubject_1756916719932014900 duration_ms=0
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=52
[GIN] 2025/09/03 - 21:55:19 | 200 |            0s |                 | POST     "/api/subjects"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=1 response_size=52
[GIN] 2025/09/03 - 21:55:19 | 200 |       1.033ms |                 | POST     "/api/section-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""

2025/09/03 21:55:19 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:57 [35;1mERROR: duplicate key value violates unique constraint "test_types_name_key" (SQLSTATE 23505)
[0m[33m[0.518ms] [34;1m[rows:0][0m INSERT INTO "test_types" ("name","created_at","updated_at") VALUES ('ZSAT','2025-09-03 21:55:19.995','2025-09-03 21:55:19.995') RETURNING "id"
2025/09/03 21:55:19 ERROR Request completed method=POST path=/api/test-types client_ip="" status_code=500 duration_ms=0 response_size=106
[GIN] 2025/09/03 - 21:55:19 | 500 |       518.4µs |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=56
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.1948ms |                 | POST     "/api/test-types"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=48
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0232ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=51
[GIN] 2025/09/03 - 21:55:19 | 200 |      1.0863ms |                 | POST     "/api/tests"
2025/09/03 21:55:19 INFO Request started method=POST path=/api/courses/2667/tests/939 client_ip="" user_agent=""
2025/09/03 21:55:19 INFO Associating test with course client_ip="" course_id=2667 test_id=939
2025/09/03 21:55:19 INFO Associating test with course course_id=2667 test_id=939
2025/09/03 21:55:20 INFO Test associated with course successfully course_id=2667 course_name=ZSATTestCourse_1756916719932014900 test_id=939 test_name=RegularTest_1756916719932014900 existing_tests_count=0 duration_ms=1
2025/09/03 21:55:20 INFO Test associated with course successfully client_ip="" course_id=2667 test_id=939 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses/2667/tests/939 client_ip="" status_code=200 duration_ms=1 response_size=85
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0282ms |                 | POST     "/api/courses/2667/tests/939"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1774 duration_ms=59
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=61 response_size=472
[GIN] 2025/09/03 - 21:55:20 | 200 |     61.6029ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=2664 role=Student test_count=6 active_filter=<nil> duration_ms=1
2025/09/03 21:55:20 INFO GetTests successful user_id=2664 client_ip="" active_filter="" test_count=6 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=2 response_size=2731
[GIN] 2025/09/03 - 21:55:20 | 200 |       2.242ms |                 | GET      "/api/tests"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=2663 role=Admin test_count=7 active_filter=<nil> duration_ms=1
2025/09/03 21:55:20 INFO GetTests successful user_id=2663 client_ip="" active_filter="" test_count=7 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=3197
[GIN] 2025/09/03 - 21:55:20 | 200 |       1.416ms |                 | GET      "/api/tests"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/enroll/2667 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2664 course_id=2667
2025/09/03 21:55:20 INFO Student enrolled in course successfully user_id=2664 course_id=2667 course_name=ZSATTestCourse_1756916719932014900 student_email=<EMAIL> is_free_course=true duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/enroll/2667 client_ip="" status_code=200 duration_ms=1 response_size=1038
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.4181ms |                 | POST     "/api/enroll/2667"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=2664 role=Student test_count=7 active_filter=<nil> duration_ms=1
2025/09/03 21:55:20 INFO GetTests successful user_id=2664 client_ip="" active_filter="" test_count=7 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=3197
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.6963ms |                 | GET      "/api/tests"

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1554 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.534ms] [34;1m[rows:0][0m DELETE FROM test_types WHERE name IN ('ZSAT', 'RegularTestType_1756916719932014900')
--- FAIL: TestGetTestsZSATBehavior (0.14s)
    test_apis_test.go:1622: ZSAT test type might already exist, response code: 500
    test_apis_test.go:1709: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1709
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "Mixed Test Type 1754843375"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+Mixed Test Type 1754843375
        	Test:       	TestGetTestsZSATBehavior
        	Messages:   	All tests should be of ZSAT type
    test_apis_test.go:1709: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1709
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "Mixed Test Type 1754843415"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+Mixed Test Type 1754843415
        	Test:       	TestGetTestsZSATBehavior
        	Messages:   	All tests should be of ZSAT type
    test_apis_test.go:1709: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1709
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "NEET Weekly"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+NEET Weekly
        	Test:       	TestGetTestsZSATBehavior
        	Messages:   	All tests should be of ZSAT type
    test_apis_test.go:1709: 
        	Error Trace:	D:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:1709
        	Error:      	Not equal: 
        	            	expected: "ZSAT"
        	            	actual  : "Results Disclosure Test Type"
        	            	
        	            	Diff:
        	            	--- Expected
        	            	+++ Actual
        	            	@@ -1 +1 @@
        	            	-ZSAT
        	            	+Results Disclosure Test Type
        	Test:       	TestGetTestsZSATBehavior
        	Messages:   	All tests should be of ZSAT type
2025/09/03 21:55:20 INFO Request started method=POST path=/api/admins client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating admin user email=<EMAIL> full_name="Topics Admin"
2025/09/03 21:55:20 INFO Admin user created successfully admin_id=2665 email=<EMAIL> full_name="Topics Admin" duration_ms=54
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/admins client_ip="" status_code=201 duration_ms=54 response_size=203
[GIN] 2025/09/03 - 21:55:20 | 201 |     54.1175ms |                 | POST     "/api/admins"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating subject subject_name=TopicsTestSubject_1756916720070251800 subject_display_name=TopicsTestSubject_1756916720070251800
2025/09/03 21:55:20 INFO Subject created successfully subject_id=3057 subject_name=TopicsTestSubject_1756916720070251800 subject_display_name=TopicsTestSubject_1756916720070251800 duration_ms=6
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=6 response_size=58
[GIN] 2025/09/03 - 21:55:20 | 200 |      6.5074ms |                 | POST     "/api/subjects"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/chapters client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating chapter chapter_name=TopicsTestChapter_1756916720070251800 chapter_display_name=TopicsTestChapter_1756916720070251800 subject_name=TopicsTestSubject_1756916720070251800
2025/09/03 21:55:20 INFO Chapter created successfully chapter_id=1341 chapter_name=TopicsTestChapter_1756916720070251800 chapter_display_name=TopicsTestChapter_1756916720070251800 subject_name=TopicsTestSubject_1756916720070251800 subject_id=3057 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/chapters client_ip="" status_code=200 duration_ms=1 response_size=58
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0276ms |                 | POST     "/api/chapters"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating topic topic_name=TopicsTestTopic1_1756916720070251800 chapter_name=TopicsTestChapter_1756916720070251800
2025/09/03 21:55:20 INFO Topic created successfully topic_name=TopicsTestTopic1_1756916720070251800 topic_id=1064 chapter_name=TopicsTestChapter_1756916720070251800 chapter_id=1341 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=57
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0254ms |                 | POST     "/api/topics"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating topic topic_name=TopicsTestTopic2_1756916720070251800 chapter_name=TopicsTestChapter_1756916720070251800
2025/09/03 21:55:20 INFO Topic created successfully topic_name=TopicsTestTopic2_1756916720070251800 topic_id=1065 chapter_name=TopicsTestChapter_1756916720070251800 chapter_id=1341 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=57
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0331ms |                 | POST     "/api/topics"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Topics retrieved successfully chapter_name=TopicsTestChapter_1756916720070251800 topic_count=2 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=409
[GIN] 2025/09/03 - 21:55:20 | 200 |         533µs |                 | GET      "/api/topics?chapter_name=TopicsTestChapter_1756916720070251800"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Topics retrieved successfully chapter_name=NonExistentChapter topic_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/topics client_ip="" status_code=200 duration_ms=0 response_size=2
[GIN] 2025/09/03 - 21:55:20 | 200 |       512.1µs |                 | GET      "/api/topics?chapter_name=NonExistentChapter"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/topics client_ip="" user_agent=""
2025/09/03 21:55:20 INFO All topics retrieved successfully topic_count=408 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/topics client_ip="" status_code=200 duration_ms=1 response_size=76885
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.1653ms |                 | GET      "/api/topics"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetTestTypes request started client_ip="" method=GET path=/api/test-types
2025/09/03 21:55:20 INFO Retrieving all test types
2025/09/03 21:55:20 INFO Test types retrieved successfully test_type_count=168 duration_ms=2
2025/09/03 21:55:20 INFO GetTestTypes completed successfully client_ip="" test_type_count=168 duration_ms=2
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/test-types client_ip="" status_code=200 duration_ms=2 response_size=29045
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.1661ms |                 | GET      "/api/test-types"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetSectionTypes request started client_ip="" method=GET path=/api/section-types
2025/09/03 21:55:20 INFO Retrieving all section types
2025/09/03 21:55:20 INFO Section types retrieved successfully section_type_count=9 duration_ms=0
2025/09/03 21:55:20 INFO GetSectionTypes completed successfully client_ip="" section_type_count=9 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=3805
[GIN] 2025/09/03 - 21:55:20 | 200 |       522.4µs |                 | GET      "/api/section-types"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetTestTypes request started client_ip="" method=GET path=/api/test-types
2025/09/03 21:55:20 INFO Retrieving all test types
2025/09/03 21:55:20 INFO Test types retrieved successfully test_type_count=167 duration_ms=1
2025/09/03 21:55:20 INFO GetTestTypes completed successfully client_ip="" test_type_count=167 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=28436
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0228ms |                 | GET      "/api/test-types"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetSectionTypes request started client_ip="" method=GET path=/api/section-types
2025/09/03 21:55:20 INFO Retrieving all section types
2025/09/03 21:55:20 INFO Section types retrieved successfully section_type_count=8 duration_ms=0
2025/09/03 21:55:20 INFO GetSectionTypes completed successfully client_ip="" section_type_count=8 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/section-types client_ip="" status_code=200 duration_ms=0 response_size=3346
[GIN] 2025/09/03 - 21:55:20 | 200 |       512.9µs |                 | GET      "/api/section-types"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests/99999/questions client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTestQuestions request test_id=99999 user_id=0 client_ip=""
2025/09/03 21:55:20 INFO Retrieving questions for test test_id=99999

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:390 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "tests" WHERE "tests"."id" = 99999 AND "tests"."deleted_at" IS NULL ORDER BY "tests"."id" LIMIT 1
2025/09/03 21:55:20 ERROR Test not found for questions retrieval test_id=99999 error="record not found" duration_ms=0
2025/09/03 21:55:20 ERROR GetTestQuestions failed test_id=99999 user_id=0 error="test with ID 99999 not found" duration_ms=0 client_ip=""
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/tests/99999/questions client_ip="" status_code=404 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:20 | 404 |            0s |                 | GET      "/api/tests/99999/questions"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests/940/questions client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTestQuestions request test_id=940 user_id=0 client_ip=""
2025/09/03 21:55:20 INFO Retrieving questions for test test_id=940
2025/09/03 21:55:20 INFO Test questions retrieved successfully test_id=940 test_name="Sample Test 1756916720" section_count=2 total_questions=3 duration_ms=1
2025/09/03 21:55:20 INFO GetTestQuestions successful test_id=940 test_name="Sample Test 1756916720" user_id=0 section_count=2 duration_ms=1 client_ip=""
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests/940/questions client_ip="" status_code=200 duration_ms=1 response_size=3197
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.6252ms |                 | GET      "/api/tests/940/questions"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests/99999/questions client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTestQuestions request test_id=99999 user_id=0 client_ip=""
2025/09/03 21:55:20 INFO Retrieving questions for test test_id=99999

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:390 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "tests" WHERE "tests"."id" = 99999 AND "tests"."deleted_at" IS NULL ORDER BY "tests"."id" LIMIT 1
2025/09/03 21:55:20 ERROR Test not found for questions retrieval test_id=99999 error="record not found" duration_ms=0
2025/09/03 21:55:20 ERROR GetTestQuestions failed test_id=99999 user_id=0 error="test with ID 99999 not found" duration_ms=0 client_ip=""
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/tests/99999/questions client_ip="" status_code=404 duration_ms=0 response_size=40
[GIN] 2025/09/03 - 21:55:20 | 404 |            0s |                 | GET      "/api/tests/99999/questions"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests/invalid/questions client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR Invalid test ID parameter test_id=invalid error="strconv.ParseUint: parsing \"invalid\": invalid syntax" client_ip=""
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/tests/invalid/questions client_ip="" status_code=400 duration_ms=0 response_size=27
[GIN] 2025/09/03 - 21:55:20 | 400 |       513.1µs |                 | GET      "/api/tests/invalid/questions"

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:2374 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.506ms] [34;1m[rows:0][0m DELETE FROM "test_types" WHERE "test_types"."id" = 1141

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:2444 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "difficulties" WHERE name = 'Medium' AND "difficulties"."deleted_at" IS NULL ORDER BY "difficulties"."id" LIMIT 1
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/941/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/941/questions
2025/09/03 21:55:20 INFO Removing questions from test test_id=941 section_name=physics_section question_ids="[1246 1247]" question_count=2
2025/09/03 21:55:20 INFO Questions removed from test successfully test_id=941 section_name=physics_section question_ids="[1246 1247]" question_count=2 duration_ms=1
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest completed successfully client_ip="" test_id=941 section_name=physics_section question_ids="[1246 1247]" question_count=2 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=DELETE path=/api/tests/941/questions client_ip="" status_code=200 duration_ms=1 response_size=54
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0295ms |                 | DELETE   "/api/tests/941/questions"
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/941/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/941/questions
2025/09/03 21:55:20 INFO Removing questions from test test_id=941 section_name=physics_section question_ids=[1246] question_count=1
2025/09/03 21:55:20 WARN Some questions not found in section test_id=941 section_name=physics_section requested_questions=[1246] found_questions=0 missing_questions=[1246] duration_ms=0
2025/09/03 21:55:20 ERROR RemoveQuestionsFromTest failed - database error client_ip="" test_id=941 section_name=physics_section question_ids=[1246] error="some questions are not associated with section 'physics_section': missing question IDs [1246]" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=DELETE path=/api/tests/941/questions client_ip="" status_code=500 duration_ms=0 response_size=105
[GIN] 2025/09/03 - 21:55:20 | 500 |       513.1µs |                 | DELETE   "/api/tests/941/questions"
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/941/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/941/questions
2025/09/03 21:55:20 INFO Removing questions from test test_id=941 section_name=nonexistent_section question_ids=[1248] question_count=1

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:545 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "sections" WHERE (test_id = 941 AND name = 'nonexistent_section') AND "sections"."deleted_at" IS NULL ORDER BY "sections"."id" LIMIT 1
2025/09/03 21:55:20 ERROR Section not found for question removal test_id=941 section_name=nonexistent_section error="record not found" duration_ms=0
2025/09/03 21:55:20 ERROR RemoveQuestionsFromTest failed - database error client_ip="" test_id=941 section_name=nonexistent_section question_ids=[1248] error="section 'nonexistent_section' not found in test with ID 941" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=DELETE path=/api/tests/941/questions client_ip="" status_code=500 duration_ms=0 response_size=71
[GIN] 2025/09/03 - 21:55:20 | 500 |         512µs |                 | DELETE   "/api/tests/941/questions"

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/cmd/server/http/test/test_apis_test.go:2605 [35;1mERROR: null value in column "test_type_id" of relation "tests" violates not-null constraint (SQLSTATE 23502)
[0m[33m[0.505ms] [34;1m[rows:0][0m DELETE FROM "test_types" WHERE "test_types"."id" = 1142
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/invalid/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/invalid/questions
2025/09/03 21:55:20 WARN RemoveQuestionsFromTest failed - invalid test ID client_ip="" test_id=invalid error="strconv.ParseUint: parsing \"invalid\": invalid syntax" duration_ms=0
2025/09/03 21:55:20 WARN Request completed method=DELETE path=/api/tests/invalid/questions client_ip="" status_code=400 duration_ms=0 response_size=27
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | DELETE   "/api/tests/invalid/questions"
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/1/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/1/questions
2025/09/03 21:55:20 WARN RemoveQuestionsFromTest failed - no question IDs provided client_ip="" test_id=1 section_name=test_section duration_ms=0
2025/09/03 21:55:20 WARN Request completed method=DELETE path=/api/tests/1/questions client_ip="" status_code=400 duration_ms=0 response_size=36
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | DELETE   "/api/tests/1/questions"
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/1/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/1/questions
2025/09/03 21:55:20 WARN RemoveQuestionsFromTest failed - invalid request body client_ip="" test_id=1 error="Key: 'RemoveQuestionsFromTestRequest.SectionName' Error:Field validation for 'SectionName' failed on the 'required' tag" duration_ms=0
2025/09/03 21:55:20 WARN Request completed method=DELETE path=/api/tests/1/questions client_ip="" status_code=400 duration_ms=0 response_size=131
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | DELETE   "/api/tests/1/questions"
2025/09/03 21:55:20 INFO Request started method=DELETE path=/api/tests/1/questions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO RemoveQuestionsFromTest request started client_ip="" method=DELETE path=/api/tests/1/questions
2025/09/03 21:55:20 INFO Removing questions from test test_id=1 section_name=test_section question_ids=[1] question_count=1

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/db/tests.go:528 [35;1mrecord not found
[0m[33m[0.000ms] [34;1m[rows:0][0m SELECT * FROM "tests" WHERE "tests"."id" = 1 AND "tests"."deleted_at" IS NULL ORDER BY "tests"."id" LIMIT 1
2025/09/03 21:55:20 ERROR Test not found for question removal test_id=1 section_name=test_section error="record not found" duration_ms=0
2025/09/03 21:55:20 ERROR RemoveQuestionsFromTest failed - database error client_ip="" test_id=1 section_name=test_section question_ids=[1] error="test with ID 1 not found" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=DELETE path=/api/tests/1/questions client_ip="" status_code=500 duration_ms=0 response_size=36
[GIN] 2025/09/03 - 21:55:20 | 500 |       519.5µs |                 | DELETE   "/api/tests/1/questions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |         513µs |                 | POST     "/api/test-types"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test IsPaid Course" price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test IsPaid Course" course_id=2668 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=39
[GIN] 2025/09/03 - 21:55:20 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:20 | 200 |       516.6µs |                 | POST     "/api/tests"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=0 response_size=41
[GIN] 2025/09/03 - 21:55:20 | 200 |       513.5µs |                 | POST     "/api/tests"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses/2668/tests/942 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Associating test with course client_ip="" course_id=2668 test_id=942
2025/09/03 21:55:20 INFO Associating test with course course_id=2668 test_id=942
2025/09/03 21:55:20 INFO Test associated with course successfully course_id=2668 course_name="Test IsPaid Course" test_id=942 test_name="Test IsPaid Paid Test" existing_tests_count=0 duration_ms=1
2025/09/03 21:55:20 INFO Test associated with course successfully client_ip="" course_id=2668 test_id=942 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses/2668/tests/942 client_ip="" status_code=200 duration_ms=1 response_size=85
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0261ms |                 | POST     "/api/courses/2668/tests/942"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses/2668/tests/943 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Associating test with course client_ip="" course_id=2668 test_id=943
2025/09/03 21:55:20 INFO Associating test with course course_id=2668 test_id=943
2025/09/03 21:55:20 WARN Attempted to associate free test with course course_id=2668 course_name="Test IsPaid Course" test_id=943 test_name="Test IsPaid Free Test" test_is_paid=false duration_ms=0
2025/09/03 21:55:20 ERROR AssociateTestWithCourse failed - database error client_ip="" course_id=2668 test_id=943 error="only paid tests can be associated with courses. Test 'Test IsPaid Free Test' (ID: 943) is a free test" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=POST path=/api/courses/2668/tests/943 client_ip="" status_code=500 duration_ms=0 response_size=113
[GIN] 2025/09/03 - 21:55:20 | 500 |       513.9µs |                 | POST     "/api/courses/2668/tests/943"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=0 role=Admin test_count=7 active_filter=<nil> duration_ms=1
2025/09/03 21:55:20 INFO GetTests successful user_id=0 client_ip="" active_filter="" test_count=7 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=2989
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0269ms |                 | GET      "/api/tests"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/subjects client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating subject subject_name="Test Subject for Results Disclosure" subject_display_name="Test Subject for Results Disclosure"
2025/09/03 21:55:20 INFO Subject created successfully subject_id=3064 subject_name="Test Subject for Results Disclosure" subject_display_name="Test Subject for Results Disclosure" duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/subjects client_ip="" status_code=200 duration_ms=0 response_size=56
[GIN] 2025/09/03 - 21:55:20 | 200 |            0s |                 | POST     "/api/subjects"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/section-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/section-types client_ip="" status_code=200 duration_ms=1 response_size=61
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0222ms |                 | POST     "/api/section-types"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/test-types client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/test-types client_ip="" status_code=200 duration_ms=1 response_size=49
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0284ms |                 | POST     "/api/test-types"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/tests client_ip="" status_code=200 duration_ms=0 response_size=43
[GIN] 2025/09/03 - 21:55:20 | 200 |       513.2µs |                 | POST     "/api/tests"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=0 role=Admin test_count=5 active_filter=<nil> duration_ms=1
2025/09/03 21:55:20 INFO GetTests successful user_id=0 client_ip="" active_filter="" test_count=5 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=2601
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0281ms |                 | GET      "/api/tests"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/tests/944/results-disclosure client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO ToggleTestResultsDisclosure request started user_id=0 client_ip=""
2025/09/03 21:55:20 INFO Toggling test results disclosure status test_id=944
2025/09/03 21:55:20 INFO Test results disclosure status toggled successfully test_id=944 previous_status=true new_status=true duration_ms=0
2025/09/03 21:55:20 INFO ToggleTestResultsDisclosure successful user_id=0 client_ip="" test_id=944 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/tests/944/results-disclosure client_ip="" status_code=200 duration_ms=0 response_size=65
[GIN] 2025/09/03 - 21:55:20 | 200 |         514µs |                 | PUT      "/api/tests/944/results-disclosure"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=0 role=Admin test_count=5 active_filter=<nil> duration_ms=1
2025/09/03 21:55:20 INFO GetTests successful user_id=0 client_ip="" active_filter="" test_count=5 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=1 response_size=2600
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0945ms |                 | GET      "/api/tests"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/tests/944/results-disclosure client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO ToggleTestResultsDisclosure request started user_id=0 client_ip=""
2025/09/03 21:55:20 INFO Toggling test results disclosure status test_id=944
2025/09/03 21:55:20 INFO Test results disclosure status toggled successfully test_id=944 previous_status=false new_status=false duration_ms=0
2025/09/03 21:55:20 INFO ToggleTestResultsDisclosure successful user_id=0 client_ip="" test_id=944 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/tests/944/results-disclosure client_ip="" status_code=200 duration_ms=0 response_size=65
[GIN] 2025/09/03 - 21:55:20 | 200 |       506.6µs |                 | PUT      "/api/tests/944/results-disclosure"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/tests client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 INFO GetTests database operation completed user_id=0 role=Admin test_count=5 active_filter=<nil> duration_ms=0
2025/09/03 21:55:20 INFO GetTests successful user_id=0 client_ip="" active_filter="" test_count=5 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/tests client_ip="" status_code=200 duration_ms=0 response_size=2601
[GIN] 2025/09/03 - 21:55:20 | 200 |       519.1µs |                 | GET      "/api/tests"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1775 duration_ms=53
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=53 response_size=491
[GIN] 2025/09/03 - 21:55:20 | 200 |     53.4266ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Paid Course" price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Paid Course" course_id=2669 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=1 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0578ms |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Free Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Free Course" course_id=2670 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |       531.9µs |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1775 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=766 student_id=1775 amount=900 course_count=1 duration_ms=6
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=766 student_id=1775 amount=900 duration_ms=6
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=6 response_size=1340
[GIN] 2025/09/03 - 21:55:20 | 200 |      6.7464ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Another Paid Course" price=2000 course_type=NEET is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Another Paid Course" course_id=2671 course_type=NEET is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=1 response_size=45
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.0963ms |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1775 amount=0 course_count=2 payment_method=CARD
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=767 student_id=1775 amount=2500 course_count=2 duration_ms=3
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=767 student_id=1775 amount=2500 duration_ms=3
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=3 response_size=1669
[GIN] 2025/09/03 - 21:55:20 | 200 |       3.351ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1775 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 ERROR Failed to create transaction student_id=1775 course_ids=[2670] error="cannot create transaction for free course: Test Free Course" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=POST path=/api/transactions client_ip="" status_code=500 duration_ms=0 response_size=71
[GIN] 2025/09/03 - 21:55:20 | 500 |       522.8µs |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 WARN Request completed method=POST path=/api/transactions client_ip="" status_code=400 duration_ms=0 response_size=46
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1775 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 ERROR Failed to create transaction student_id=1775 course_ids=[99999] error="some courses not found" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=POST path=/api/transactions client_ip="" status_code=500 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:20 | 500 |       519.2µs |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 WARN Request completed method=POST path=/api/transactions client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:20 | 401 |            0s |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1776 duration_ms=61
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=61 response_size=491
[GIN] 2025/09/03 - 21:55:20 | 200 |     61.9777ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Paid Course" price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Paid Course" course_id=2672 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Free Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Free Course" course_id=2673 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |         519µs |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1776 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=768 student_id=1776 amount=900 course_count=1 duration_ms=1
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=768 student_id=1776 amount=900 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=1 response_size=1338
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.6113ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/768/status client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Updating transaction status transaction_id=768 status=COMPLETED payment_reference=txn_123456
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=768 status=COMPLETED duration_ms=0
2025/09/03 21:55:20 INFO Auto-enrolling student in purchased courses transaction_id=768
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2667 course_id=2672
2025/09/03 21:55:20 INFO Checking transaction for paid course enrollment user_id=2667 course_id=2672 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Found valid transaction for course enrollment transaction_id=768 student_id=1776 course_id=2672
2025/09/03 21:55:20 INFO Student enrolled in course successfully user_id=2667 course_id=2672 course_name="Test Paid Course" student_email=<EMAIL> is_free_course=false duration_ms=1
2025/09/03 21:55:20 INFO Successfully auto-enrolled student in course transaction_id=768 course_id=2672 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Auto-enrollment completed successfully transaction_id=768 enrolled_courses=1 duration_ms=2
2025/09/03 21:55:20 INFO Successfully auto-enrolled student in purchased courses transaction_id=768
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=768 status=COMPLETED duration_ms=2
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/transactions/768/status client_ip="" status_code=200 duration_ms=2 response_size=53
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.5795ms |                 | PUT      "/api/transactions/768/status"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1776 amount=0 course_count=1 payment_method=CARD
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=769 student_id=1776 amount=900 course_count=1 duration_ms=1
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=769 student_id=1776 amount=900 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=1 response_size=1339
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.1973ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Retrieved transaction history successfully student_id=1776 transaction_count=2 total_amount=900 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/transactions client_ip="" status_code=200 duration_ms=0 response_size=771
[GIN] 2025/09/03 - 21:55:20 | 200 |       902.1µs |                 | GET      "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/transactions client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:20 | 401 |            0s |                 | GET      "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1777 duration_ms=60
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=60 response_size=491
[GIN] 2025/09/03 - 21:55:20 | 200 |     60.1027ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Paid Course" price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Paid Course" course_id=2674 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |       506.5µs |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Free Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Free Course" course_id=2675 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |       516.1µs |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1777 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=770 student_id=1777 amount=900 course_count=1 duration_ms=1
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=770 student_id=1777 amount=900 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=1 response_size=1338
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.6823ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions/770 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Retrieved transaction successfully transaction_id=770 student_id=1777 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/transactions/770 client_ip="" status_code=200 duration_ms=0 response_size=1338
[GIN] 2025/09/03 - 21:55:20 | 200 |       700.9µs |                 | GET      "/api/transactions/770"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions/99999 client_ip="" user_agent=""

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/db/transactions.go:231 [35;1mrecord not found
[0m[33m[0.505ms] [34;1m[rows:0][0m SELECT * FROM "transactions" WHERE "transactions"."id" = 99999 AND "transactions"."deleted_at" IS NULL ORDER BY "transactions"."id" LIMIT 1
2025/09/03 21:55:20 ERROR Failed to retrieve transaction transaction_id=99999 error="record not found" duration_ms=0
2025/09/03 21:55:20 ERROR Failed to retrieve transaction transaction_id=99999 error="record not found" duration_ms=0
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/transactions/99999 client_ip="" status_code=404 duration_ms=0 response_size=33
[GIN] 2025/09/03 - 21:55:20 | 404 |       504.9µs |                 | GET      "/api/transactions/99999"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions/770 client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR failed to parse token error="token contains an invalid number of segments"
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/transactions/770 client_ip="" status_code=401 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:20 | 401 |            0s |                 | GET      "/api/transactions/770"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions/invalid client_ip="" user_agent=""
2025/09/03 21:55:20 WARN Request completed method=GET path=/api/transactions/invalid client_ip="" status_code=400 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | GET      "/api/transactions/invalid"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1778 duration_ms=56
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=56 response_size=491
[GIN] 2025/09/03 - 21:55:20 | 200 |     61.6923ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Paid Course" price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Paid Course" course_id=2676 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |       505.4µs |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Free Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Free Course" course_id=2677 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1778 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=771 student_id=1778 amount=900 course_count=1 duration_ms=0
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=771 student_id=1778 amount=900 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=2 response_size=1340
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.0041ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/771/status client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Updating transaction status transaction_id=771 status=COMPLETED payment_reference=txn_123456
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=771 status=COMPLETED duration_ms=0
2025/09/03 21:55:20 INFO Auto-enrolling student in purchased courses transaction_id=771
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2669 course_id=2676
2025/09/03 21:55:20 INFO Checking transaction for paid course enrollment user_id=2669 course_id=2676 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Found valid transaction for course enrollment transaction_id=771 student_id=1778 course_id=2676
2025/09/03 21:55:20 INFO Student enrolled in course successfully user_id=2669 course_id=2676 course_name="Test Paid Course" student_email=<EMAIL> is_free_course=false duration_ms=1
2025/09/03 21:55:20 INFO Successfully auto-enrolled student in course transaction_id=771 course_id=2676 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Auto-enrollment completed successfully transaction_id=771 enrolled_courses=1 duration_ms=2
2025/09/03 21:55:20 INFO Successfully auto-enrolled student in purchased courses transaction_id=771
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=771 status=COMPLETED duration_ms=2
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/transactions/771/status client_ip="" status_code=200 duration_ms=2 response_size=53
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.0494ms |                 | PUT      "/api/transactions/771/status"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions/771 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Retrieved transaction successfully transaction_id=771 student_id=1778 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/transactions/771 client_ip="" status_code=200 duration_ms=0 response_size=1352
[GIN] 2025/09/03 - 21:55:20 | 200 |       517.9µs |                 | GET      "/api/transactions/771"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/771/status client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Updating transaction status transaction_id=771 status=FAILED payment_reference=""
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=771 status=FAILED duration_ms=0
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=771 status=FAILED duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/transactions/771/status client_ip="" status_code=200 duration_ms=0 response_size=53
[GIN] 2025/09/03 - 21:55:20 | 200 |       517.5µs |                 | PUT      "/api/transactions/771/status"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/transactions/771 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Retrieved transaction successfully transaction_id=771 student_id=1778 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/transactions/771 client_ip="" status_code=200 duration_ms=0 response_size=1349
[GIN] 2025/09/03 - 21:55:20 | 200 |       529.8µs |                 | GET      "/api/transactions/771"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/99999/status client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Updating transaction status transaction_id=99999 status=COMPLETED payment_reference=""
2025/09/03 21:55:20 ERROR Failed to update transaction status transaction_id=99999 status=COMPLETED error="transaction not found" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=PUT path=/api/transactions/99999/status client_ip="" status_code=500 duration_ms=0 response_size=33
[GIN] 2025/09/03 - 21:55:20 | 500 |        89.4µs |                 | PUT      "/api/transactions/99999/status"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/771/status client_ip="" user_agent=""
2025/09/03 21:55:20 WARN Request completed method=PUT path=/api/transactions/771/status client_ip="" status_code=400 duration_ms=0 response_size=111
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | PUT      "/api/transactions/771/status"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/invalid/status client_ip="" user_agent=""
2025/09/03 21:55:20 WARN Request completed method=PUT path=/api/transactions/invalid/status client_ip="" status_code=400 duration_ms=0 response_size=34
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | PUT      "/api/transactions/invalid/status"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1779 duration_ms=59
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=60 response_size=491
[GIN] 2025/09/03 - 21:55:20 | 200 |     60.3146ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Paid Course" price=1000 course_type=IIT-JEE is_free=false subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Paid Course" course_id=2678 course_type=IIT-JEE is_free=false subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |         505µs |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/courses client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating course name="Test Free Course" price=0 course_type=NEET is_free=true subject_count=0
2025/09/03 21:55:20 INFO Course created successfully name="Test Free Course" course_id=2679 course_type=NEET is_free=true subject_count=0 duration_ms=0
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/courses client_ip="" status_code=200 duration_ms=0 response_size=37
[GIN] 2025/09/03 - 21:55:20 | 200 |            0s |                 | POST     "/api/courses"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/enroll/2678 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2670 course_id=2678
2025/09/03 21:55:20 INFO Checking transaction for paid course enrollment user_id=2670 course_id=2678 course_name="Test Paid Course"
2025/09/03 21:55:20 ERROR No valid transaction found for paid course enrollment student_id=1779 course_id=2678 course_name="Test Paid Course" duration_ms=1
2025/09/03 21:55:20 ERROR Request completed method=POST path=/api/enroll/2678 client_ip="" status_code=500 duration_ms=1 response_size=92
[GIN] 2025/09/03 - 21:55:20 | 500 |      1.0353ms |                 | POST     "/api/enroll/2678"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/enroll/2679 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2670 course_id=2679
2025/09/03 21:55:20 INFO Student enrolled in course successfully user_id=2670 course_id=2679 course_name="Test Free Course" student_email=<EMAIL> is_free_course=true duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/enroll/2679 client_ip="" status_code=200 duration_ms=1 response_size=1040
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.1009ms |                 | POST     "/api/enroll/2679"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/transactions client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating transaction student_id=1779 amount=0 course_count=1 payment_method=UPI
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=772 student_id=1779 amount=900 course_count=1 duration_ms=1
2025/09/03 21:55:20 INFO Transaction created successfully transaction_id=772 student_id=1779 amount=900 duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/transactions client_ip="" status_code=200 duration_ms=1 response_size=1340
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.3474ms |                 | POST     "/api/transactions"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/enroll/2678 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2670 course_id=2678
2025/09/03 21:55:20 INFO Checking transaction for paid course enrollment user_id=2670 course_id=2678 course_name="Test Paid Course"
2025/09/03 21:55:20 ERROR No valid transaction found for paid course enrollment student_id=1779 course_id=2678 course_name="Test Paid Course" duration_ms=0
2025/09/03 21:55:20 ERROR Request completed method=POST path=/api/enroll/2678 client_ip="" status_code=500 duration_ms=0 response_size=92
[GIN] 2025/09/03 - 21:55:20 | 500 |       504.9µs |                 | POST     "/api/enroll/2678"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/transactions/772/status client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Updating transaction status transaction_id=772 status=COMPLETED payment_reference=txn_integration_test
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=772 status=COMPLETED duration_ms=0
2025/09/03 21:55:20 INFO Auto-enrolling student in purchased courses transaction_id=772
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2670 course_id=2678
2025/09/03 21:55:20 INFO Checking transaction for paid course enrollment user_id=2670 course_id=2678 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Found valid transaction for course enrollment transaction_id=772 student_id=1779 course_id=2678
2025/09/03 21:55:20 INFO Student enrolled in course successfully user_id=2670 course_id=2678 course_name="Test Paid Course" student_email=<EMAIL> is_free_course=false duration_ms=1
2025/09/03 21:55:20 INFO Successfully auto-enrolled student in course transaction_id=772 course_id=2678 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Auto-enrollment completed successfully transaction_id=772 enrolled_courses=1 duration_ms=1
2025/09/03 21:55:20 INFO Successfully auto-enrolled student in purchased courses transaction_id=772
2025/09/03 21:55:20 INFO Transaction status updated successfully transaction_id=772 status=COMPLETED duration_ms=2
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/transactions/772/status client_ip="" status_code=200 duration_ms=2 response_size=53
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.0547ms |                 | PUT      "/api/transactions/772/status"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/enroll/2678 client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Enrolling student in course user_id=2670 course_id=2678
2025/09/03 21:55:20 INFO Student already enrolled in course user_id=2670 course_id=2678 course_name="Test Paid Course"
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/enroll/2678 client_ip="" status_code=200 duration_ms=0 response_size=1379
[GIN] 2025/09/03 - 21:55:20 | 200 |       528.8µs |                 | POST     "/api/enroll/2678"
2025/09/03 21:55:20 INFO Request started method=POST path=/api/students client_ip="" user_agent=""
2025/09/03 21:55:20 INFO Creating student email=<EMAIL>
2025/09/03 21:55:20 INFO Student created successfully email=<EMAIL> student_id=1780 duration_ms=60
2025/09/03 21:55:20 INFO Request completed method=POST path=/api/students client_ip="" status_code=200 duration_ms=60 response_size=540
[GIN] 2025/09/03 - 21:55:20 | 200 |     60.1811ms |                 | POST     "/api/students"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/video-progress client_ip="" user_agent=""
2025/09/03 21:55:20 INFO UpdateVideoProgress request started client_ip="" student_id=1780 video_id=260 progress_seconds=120 progress_percent=25.5
2025/09/03 21:55:20 INFO Updating video progress student_id=1780 video_id=260 progress_seconds=120 progress_percent=25.5

2025/09/03 21:55:20 [31;1mD:/go/src/ziaacademy-backend/db/video_progress.go:51 [35;1mrecord not found
[0m[33m[0.517ms] [34;1m[rows:0][0m SELECT * FROM "video_progresses" WHERE (student_id = 1780 AND video_id = 260) AND "video_progresses"."deleted_at" IS NULL ORDER BY "video_progresses"."id" LIMIT 1
2025/09/03 21:55:20 INFO Created new video progress record student_id=1780 video_id=260 progress_id=16
2025/09/03 21:55:20 INFO Video progress updated successfully student_id=1780 video_id=260 progress_percent=25.5 is_completed=false duration_ms=1
2025/09/03 21:55:20 INFO UpdateVideoProgress successful client_ip="" student_id=1780 video_id=260 progress_percent=25.5 is_completed=false duration_ms=2
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/video-progress client_ip="" status_code=200 duration_ms=2 response_size=49
[GIN] 2025/09/03 - 21:55:20 | 200 |      2.0532ms |                 | PUT      "/api/video-progress"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/video-progress client_ip="" user_agent=""
2025/09/03 21:55:20 INFO UpdateVideoProgress request started client_ip="" student_id=1780 video_id=260 progress_seconds=360 progress_percent=75
2025/09/03 21:55:20 INFO Updating video progress student_id=1780 video_id=260 progress_seconds=360 progress_percent=75
2025/09/03 21:55:20 INFO Updated existing video progress record student_id=1780 video_id=260 progress_id=16
2025/09/03 21:55:20 INFO Video progress updated successfully student_id=1780 video_id=260 progress_percent=75 is_completed=false duration_ms=1
2025/09/03 21:55:20 INFO UpdateVideoProgress successful client_ip="" student_id=1780 video_id=260 progress_percent=75 is_completed=false duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/video-progress client_ip="" status_code=200 duration_ms=1 response_size=49
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.5607ms |                 | PUT      "/api/video-progress"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/video-progress client_ip="" user_agent=""
2025/09/03 21:55:20 INFO UpdateVideoProgress request started client_ip="" student_id=1780 video_id=260 progress_seconds=480 progress_percent=100
2025/09/03 21:55:20 INFO Updating video progress student_id=1780 video_id=260 progress_seconds=480 progress_percent=100
2025/09/03 21:55:20 INFO Updated existing video progress record student_id=1780 video_id=260 progress_id=16
2025/09/03 21:55:20 INFO Video progress updated successfully student_id=1780 video_id=260 progress_percent=100 is_completed=true duration_ms=1
2025/09/03 21:55:20 INFO UpdateVideoProgress successful client_ip="" student_id=1780 video_id=260 progress_percent=100 is_completed=true duration_ms=1
2025/09/03 21:55:20 INFO Request completed method=PUT path=/api/video-progress client_ip="" status_code=200 duration_ms=1 response_size=49
[GIN] 2025/09/03 - 21:55:20 | 200 |      1.1361ms |                 | PUT      "/api/video-progress"
2025/09/03 21:55:20 INFO Request started method=GET path=/api/content client_ip="" user_agent=""
2025/09/03 21:55:20 INFO GetContent request started client_ip="" user_id=2671 user_role=Student is_student=true student_id=1780 course_id=<nil>
2025/09/03 21:55:20 INFO Retrieving content organized by subjects with progress student_id=1780 course_id=<nil>
2025/09/03 21:55:20 INFO Content by subjects with progress retrieved successfully student_id=1780 course_id=<nil> subject_count=13 total_videos=13 total_pdfs=4 duration_ms=19
2025/09/03 21:55:20 INFO GetContent successful client_ip="" student_id=1780 course_id=<nil> subject_count=13 total_videos=13 total_materials=4 duration_ms=19
2025/09/03 21:55:20 INFO Request completed method=GET path=/api/content client_ip="" status_code=200 duration_ms=19 response_size=6478
[GIN] 2025/09/03 - 21:55:20 | 200 |     19.6113ms |                 | GET      "/api/content"
2025/09/03 21:55:20 INFO Request started method=PUT path=/api/video-progress client_ip="" user_agent=""
2025/09/03 21:55:20 ERROR UpdateVideoProgress failed - invalid request body client_ip="" student_id=1780 error="Key: 'VideoProgressForUpdate.ProgressSeconds' Error:Field validation for 'ProgressSeconds' failed on the 'min' tag\nKey: 'VideoProgressForUpdate.ProgressPercent' Error:Field validation for 'ProgressPercent' failed on the 'max' tag\nKey: 'VideoProgressForUpdate.VideoDuration' Error:Field validation for 'VideoDuration' failed on the 'required' tag" duration_ms=0
2025/09/03 21:55:20 WARN Request completed method=PUT path=/api/video-progress client_ip="" status_code=400 duration_ms=0 response_size=359
[GIN] 2025/09/03 - 21:55:20 | 400 |            0s |                 | PUT      "/api/video-progress"
FAIL
FAIL	ziaacademy-backend/cmd/server/http/test	3.710s
ok  	ziaacademy-backend/db	(cached)
?   	ziaacademy-backend/docs	[no test files]
?   	ziaacademy-backend/internal/configs	[no test files]
?   	ziaacademy-backend/internal/middleware	[no test files]
?   	ziaacademy-backend/internal/models	[no test files]
?   	ziaacademy-backend/internal/token	[no test files]
FAIL
2025-09-03 21:55:21 | ----- Output End -----
