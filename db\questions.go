package db

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateTopic(ctx context.Context, topic *models.Topic, chapterName string) (*models.Topic, error) {
	start := time.Now()
	slog.Info("Creating topic", "topic_name", topic.Name, "chapter_name", chapterName)

	// Find the chapter by name
	var chapter models.Chapter
	if err := p.db.Where("name = ?", chapterName).First(&chapter).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Chapter not found for topic creation",
			"topic_name", topic.Name,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
	}

	// Set the chapter ID
	topic.ChapterID = chapter.ID

	res := p.db.Create(topic)
	if res.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to create topic",
			"topic_name", topic.Name,
			"chapter_name", chapterName,
			"chapter_id", chapter.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	// Load the topic with chapter association
	if err := p.db.Preload("Chapter").First(topic, topic.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load topic with chapter association",
			"topic_name", topic.Name,
			"topic_id", topic.ID,
			"chapter_name", chapterName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to load topic with chapter: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Topic created successfully",
		"topic_name", topic.Name,
		"topic_id", topic.ID,
		"chapter_name", chapterName,
		"chapter_id", chapter.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return topic, nil
}

func (p *DbPlugin) CreateQuestion(ctx context.Context, question *models.Question, topicName, difficultyName string, options []models.OptionForCreate) (*models.Question, error) {
	start := time.Now()
	slog.Info("Creating question",
		"question_text", question.Text,
		"topic_name", topicName,
		"difficulty_name", difficultyName,
	)

	// Find the topic by name
	var topic models.Topic
	if err := p.db.Where("name = ?", topicName).First(&topic).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Topic not found for question creation",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("topic '%s' not found: %w", topicName, err)
	}

	// Find the difficulty by name
	var difficulty models.Difficulty
	if err := p.db.Where("name = ?", difficultyName).First(&difficulty).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Difficulty not found for question creation",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("difficulty '%s' not found: %w", difficultyName, err)
	}

	// Set the IDs
	question.TopicID = topic.ID
	question.DifficultyID = difficulty.ID

	slog.Debug("Creating question with associations",
		"topic_id", topic.ID,
		"topic_name", topicName,
		"difficulty_id", difficulty.ID,
		"difficulty_name", difficultyName,
	)

	// Start a transaction to ensure atomicity
	tx := p.db.Begin()
	if tx.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to start transaction for question creation",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", tx.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, tx.Error
	}

	// Create the question
	res := tx.Create(question)
	if res.Error != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create question",
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"topic_id", topic.ID,
			"difficulty_id", difficulty.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	// Create options if provided
	if len(options) > 0 {
		slog.Debug("Creating options for question",
			"question_id", question.ID,
			"option_count", len(options),
		)

		for i, optionInput := range options {
			option := models.Option{
				QuestionID:     question.ID,
				OptionText:     optionInput.OptionText,
				OptionImageURL: optionInput.OptionImageURL,
				IsCorrect:      optionInput.IsCorrect,
			}

			if err := tx.Create(&option).Error; err != nil {
				tx.Rollback()
				duration := time.Since(start)
				slog.Error("Failed to create option",
					"question_id", question.ID,
					"option_index", i,
					"option_text", optionInput.OptionText,
					"error", err.Error(),
					"duration_ms", duration.Milliseconds(),
				)
				return nil, fmt.Errorf("failed to create option %d: %w", i, err)
			}
		}

		slog.Debug("Options created successfully",
			"question_id", question.ID,
			"option_count", len(options),
		)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit transaction",
			"question_id", question.ID,
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Load the question with all associations
	if err := p.db.Preload("Topic").Preload("Difficulty").Preload("Options").First(question, question.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load question with associations",
			"question_id", question.ID,
			"topic_name", topicName,
			"difficulty_name", difficultyName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to load question with associations: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Question created successfully",
		"question_id", question.ID,
		"question_text", question.Text,
		"topic_name", topicName,
		"topic_id", topic.ID,
		"difficulty_name", difficultyName,
		"difficulty_id", difficulty.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return question, nil
}

func (p *DbPlugin) GetQuestions(ctx context.Context, subjectName, chapterName, topicName, difficulty string) ([]models.Question, error) {
	start := time.Now()
	slog.Debug("Retrieving questions with filters",
		"subject_name", subjectName,
		"chapter_name", chapterName,
		"topic_name", topicName,
		"difficulty", difficulty,
	)

	var questions []models.Question

	// Build the query with joins
	query := p.db.Preload("Topic.Chapter.Subject").
		Preload("Difficulty").
		Preload("Options").
		Joins("JOIN topics ON topics.id = questions.topic_id").
		Joins("JOIN chapters ON chapters.id = topics.chapter_id").
		Joins("JOIN subjects ON subjects.id = chapters.subject_id").
		Joins("JOIN difficulties ON difficulties.id = questions.difficulty_id")

	// Build WHERE conditions dynamically based on provided parameters
	var conditions []string
	var args []interface{}

	if subjectName != "" {
		conditions = append(conditions, "subjects.name = ?")
		args = append(args, subjectName)
		slog.Debug("Applied subject filter", "subject_name", subjectName)
	}

	if chapterName != "" {
		conditions = append(conditions, "chapters.name = ?")
		args = append(args, chapterName)
		slog.Debug("Applied chapter filter", "chapter_name", chapterName)
	}

	if topicName != "" {
		conditions = append(conditions, "topics.name = ?")
		args = append(args, topicName)
		slog.Debug("Applied topic filter", "topic_name", topicName)
	}

	if difficulty != "" {
		conditions = append(conditions, "difficulties.name = ?")
		args = append(args, difficulty)
		slog.Debug("Applied difficulty filter", "difficulty", difficulty)
	}

	// Apply WHERE conditions if any exist
	if len(conditions) > 0 {
		whereClause := strings.Join(conditions, " AND ")
		query = query.Where(whereClause, args...)
	}

	err := query.Find(&questions).Error
	duration := time.Since(start)

	if err != nil {
		slog.Error("Failed to retrieve questions",
			"subject_name", subjectName,
			"chapter_name", chapterName,
			"topic_name", topicName,
			"difficulty", difficulty,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to get questions: %w", err)
	}

	slog.Info("Questions retrieved successfully",
		"subject_name", subjectName,
		"chapter_name", chapterName,
		"topic_name", topicName,
		"difficulty", difficulty,
		"question_count", len(questions),
		"duration_ms", duration.Milliseconds(),
	)
	return questions, nil
}

// GetTopics retrieves topics for a given chapter name, or all topics if chapterName is empty
func (p *DbPlugin) GetTopics(ctx context.Context, chapterName string) ([]models.TopicSummary, error) {
	start := time.Now()

	var topics []models.TopicSummary
	query := p.db.Table("topics").
		Select("topics.id, topics.name, subjects.name as subject_name, subjects.id as subject_id, chapters.name as chapter_name, chapters.id as chapter_id").
		Joins("JOIN chapters ON chapters.id = topics.chapter_id").
		Joins("JOIN subjects ON subjects.id = chapters.subject_id")

	if chapterName != "" {
		slog.Debug("Retrieving topics for chapter", "chapter_name", chapterName)
		query = query.Where("chapters.name = ?", chapterName)
	} else {
		slog.Debug("Retrieving all topics")
	}

	if err := query.Find(&topics).Error; err != nil {
		duration := time.Since(start)
		if chapterName != "" {
			slog.Error("Failed to retrieve topics for chapter",
				"chapter_name", chapterName,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
		} else {
			slog.Error("Failed to retrieve all topics",
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
		}
		return nil, err
	}

	duration := time.Since(start)
	if chapterName != "" {
		slog.Info("Topics retrieved successfully",
			"chapter_name", chapterName,
			"topic_count", len(topics),
			"duration_ms", duration.Milliseconds(),
		)
	} else {
		slog.Info("All topics retrieved successfully",
			"topic_count", len(topics),
			"duration_ms", duration.Milliseconds(),
		)
	}
	return topics, nil
}
