package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// CreateFormulaCards creates multiple formula cards for a topic in a single transaction
func (p *DbPlugin) CreateFormulaCards(ctx context.Context, formulaCardsInput *models.FormulaCardsForCreate) ([]models.FormulaCard, error) {
	start := time.Now()
	slog.Info("Creating formula cards",
		"subject_name", formulaCardsInput.SubjectName,
		"chapter_name", formulaCardsInput.ChapterName,
		"topic_name", formulaCardsInput.TopicName,
		"card_count", len(formulaCardsInput.FormulaCards),
	)

	// Validate input before starting transaction
	if len(formulaCardsInput.FormulaCards) == 0 {
		return nil, fmt.Errorf("no formula cards provided")
	}

	// Find the topic by name, chapter name, and subject name before starting transaction
	var topic models.Topic
	if err := p.db.Joins("JOIN chapters ON topics.chapter_id = chapters.id").
		Joins("JOIN subjects ON chapters.subject_id = subjects.id").
		Where("topics.name = ? AND chapters.name = ? AND subjects.name = ?",
			formulaCardsInput.TopicName, formulaCardsInput.ChapterName, formulaCardsInput.SubjectName).
		First(&topic).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Topic not found for formula cards creation",
			"subject_name", formulaCardsInput.SubjectName,
			"chapter_name", formulaCardsInput.ChapterName,
			"topic_name", formulaCardsInput.TopicName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("topic '%s' in chapter '%s' of subject '%s' not found: %w",
			formulaCardsInput.TopicName, formulaCardsInput.ChapterName, formulaCardsInput.SubjectName, err)
	}

	// Start transaction for creating formula cards
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			slog.Error("Panic during formula cards creation",
				"subject_name", formulaCardsInput.SubjectName,
				"chapter_name", formulaCardsInput.ChapterName,
				"topic_name", formulaCardsInput.TopicName,
				"panic", r,
			)
			tx.Rollback()
		}
	}()

	var createdCards []models.FormulaCard

	// Create each formula card
	for _, cardInput := range formulaCardsInput.FormulaCards {
		formulaCard := models.FormulaCard{
			Name:     cardInput.Name,
			ImageUrl: cardInput.ImageUrl,
			TopicID:  topic.ID,
		}

		if err := tx.Create(&formulaCard).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to create formula card",
				"subject_name", formulaCardsInput.SubjectName,
				"chapter_name", formulaCardsInput.ChapterName,
				"topic_name", formulaCardsInput.TopicName,
				"card_name", cardInput.Name,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to create formula card '%s': %w", cardInput.Name, err)
		}

		// Load the formula card with topic association (including chapter and subject)
		if err := tx.Preload("Topic.Chapter.Subject").First(&formulaCard, formulaCard.ID).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to load formula card with topic association",
				"card_id", formulaCard.ID,
				"card_name", cardInput.Name,
				"subject_name", formulaCardsInput.SubjectName,
				"chapter_name", formulaCardsInput.ChapterName,
				"topic_name", formulaCardsInput.TopicName,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to load formula card with topic: %w", err)
		}

		createdCards = append(createdCards, formulaCard)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit formula cards creation transaction",
			"subject_name", formulaCardsInput.SubjectName,
			"chapter_name", formulaCardsInput.ChapterName,
			"topic_name", formulaCardsInput.TopicName,
			"card_count", len(formulaCardsInput.FormulaCards),
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Formula cards created successfully",
		"subject_name", formulaCardsInput.SubjectName,
		"chapter_name", formulaCardsInput.ChapterName,
		"topic_name", formulaCardsInput.TopicName,
		"topic_id", topic.ID,
		"card_count", len(createdCards),
		"duration_ms", duration.Milliseconds(),
	)

	return createdCards, nil
}

// GetAllFormulaCardsOrganizedBySubject retrieves all formula cards organized by subject, chapter, and topic with counts
func (p *DbPlugin) GetAllFormulaCardsOrganizedBySubject(ctx context.Context) ([]models.FormulaCardsBySubject, error) {
	start := time.Now()
	slog.Debug("Retrieving all formula cards organized by subject, chapter, and topic")

	var formulaCards []models.FormulaCard

	// Get all formula cards with their topics, chapters, and subjects
	err := p.db.Preload("Topic.Chapter.Subject").Find(&formulaCards).Error

	duration := time.Since(start)

	if err != nil {
		slog.Error("Failed to retrieve all formula cards",
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve formula cards: %w", err)
	}

	// Organize formula cards hierarchically: subject -> chapter -> topic
	subjectMap := make(map[string]map[string]map[string][]models.FormulaCardSummary)

	for _, card := range formulaCards {
		subjectName := card.Topic.Chapter.Subject.Name
		chapterName := card.Topic.Chapter.Name
		topicName := card.Topic.Name

		cardSummary := models.FormulaCardSummary{
			ID:          card.ID,
			Name:        card.Name,
			SubjectName: subjectName,
			ChapterName: chapterName,
			TopicName:   topicName,
			ImageUrl:    card.ImageUrl,
		}

		// Initialize nested maps if they don't exist
		if subjectMap[subjectName] == nil {
			subjectMap[subjectName] = make(map[string]map[string][]models.FormulaCardSummary)
		}
		if subjectMap[subjectName][chapterName] == nil {
			subjectMap[subjectName][chapterName] = make(map[string][]models.FormulaCardSummary)
		}

		subjectMap[subjectName][chapterName][topicName] = append(subjectMap[subjectName][chapterName][topicName], cardSummary)
	}

	// Convert nested maps to hierarchical structure
	var result []models.FormulaCardsBySubject
	for subjectName, chapterMap := range subjectMap {
		var chapters []models.FormulaCardsByChapter
		subjectCount := 0

		for chapterName, topicMap := range chapterMap {
			var topics []models.FormulaCardsByTopic
			chapterCount := 0

			for topicName, cards := range topicMap {
				topicCount := len(cards)
				chapterCount += topicCount

				topics = append(topics, models.FormulaCardsByTopic{
					TopicName:    topicName,
					FormulaCards: cards,
					Count:        topicCount,
				})
			}

			subjectCount += chapterCount
			chapters = append(chapters, models.FormulaCardsByChapter{
				ChapterName: chapterName,
				Topics:      topics,
				Count:       chapterCount,
			})
		}

		result = append(result, models.FormulaCardsBySubject{
			SubjectName: subjectName,
			Chapters:    chapters,
			Count:       subjectCount,
		})
	}

	slog.Debug("Formula cards organized hierarchically retrieved successfully",
		"subject_count", len(result),
		"total_card_count", len(formulaCards),
		"duration_ms", duration.Milliseconds(),
	)

	return result, nil
}
