# API Usage Examples

## 1. Create Multiple Subjects (Bulk Creation)

### Endpoint
```
POST /api/subjects/bulk
```

### Request Body
```json
{
  "subjects": [
    {
      "name": "Physics",
      "displayName": "Physics"
    },
    {
      "name": "Chemistry", 
      "displayName": "Chemistry"
    },
    {
      "name": "Mathematics",
      "displayName": "Mathematics"
    }
  ]
}
```

### Response
```json
[
  {
    "id": 1,
    "name": "Physics"
  },
  {
    "id": 2,
    "name": "Chemistry"
  },
  {
    "id": 3,
    "name": "Mathematics"
  }
]
```

## 2. Create Course with Existing Subjects

### Endpoint
```
POST /api/courses
```

### Request Body (New Format)
```json
{
  "name": "IIT-JEE Foundation Course",
  "description": "Complete foundation course for IIT-JEE preparation",
  "price": 15000,
  "discount": 10.5,
  "durationInDays": 365,
  "isFree": false,
  "courseType": "IIT-JEE",
  "subjectNames": ["Physics", "Chemistry", "Mathematics"]
}
```

### Response
```json
{
  "id": 1,
  "name": "IIT-JEE Foundation Course"
}
```

## Key Changes Made

1. **New Bulk Subjects API**: Added `/api/subjects/bulk` endpoint to create multiple subjects at once
2. **Modified Course Creation**: Course creation now accepts `subjectNames` array instead of `subjects` array
3. **Validation**: The system validates that all referenced subjects exist before creating the course
4. **Transaction Safety**: Both bulk subject creation and course creation use database transactions for data consistency

## Benefits

- **Efficiency**: Create multiple subjects in a single API call
- **Data Integrity**: Courses can only reference existing subjects
- **Better Separation**: Subject creation is separate from course creation
- **Validation**: Clear error messages when referenced subjects don't exist
