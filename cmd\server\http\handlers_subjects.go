package http

import (
	"net/http"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// GetSubjects godoc
//
//	@Summary		Get Subjects
//	@Description	get subjects for logged in student
//	     @Security       BearerAuth
//	@Tags			library
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.Subject
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/subjects [get]
//
// GetQuestions is the HTTP handler to get list of questions for
// given topic and difficulty level
// GetSubjects is the HTTP handler to get subjects for a student
// This handler does not use any framework, instead just the standard library
func (h *Handlers) GetSubjects(ctx *gin.Context) {
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	subjects, err := h.db.GetSubjects(ctx.Request.Context(), userID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, gin.H{"subjects": subjects})
}

// CreateSubject godoc
//
//		@Summary		CreateSubject
//		@Description	create new subject
//		@Description
//		@Description	Field Constraints:
//		@Description	- name: Subject name must be unique (required)
//		@Description	- displayName: Display name for the subject (required)
//	     @Security       BearerAuth
//	 @Param			item	body	models.SubjectForCreate	true	"subject details"
//		@Tags			subjects
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/subjects [post]
func (h *Handlers) CreateSubject(ctx *gin.Context) {
	subjectInput := new(models.SubjectForCreate)
	if err := ctx.ShouldBindJSON(subjectInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	subject := &models.Subject{
		Name:        subjectInput.Name,
		DisplayName: subjectInput.DisplayName,
	}

	createdSubject, err := h.db.CreateSubject(ctx.Request.Context(), subject)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdSubject.ID,
		Name: createdSubject.Name,
	}
	ctx.JSON(http.StatusOK, response)
}
