package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
)

// UpdateStudyMaterialProgress updates or creates study material progress for a student
func (p *DbPlugin) UpdateStudyMaterialProgress(ctx context.Context, studentID uint, progressData *models.StudyMaterialProgressForUpdate) error {
	start := time.Now()
	slog.Info("Updating study material progress",
		"student_id", studentID,
		"study_material_id", progressData.StudyMaterialID,
	)

	// Verify that the study material exists
	var studyMaterial models.StudyMaterial
	if err := p.db.First(&studyMaterial, progressData.StudyMaterialID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("UpdateStudyMaterialProgress failed - study material not found",
			"student_id", studentID,
			"study_material_id", progressData.StudyMaterialID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("study material with ID %d not found: %w", progressData.StudyMaterialID, err)
	}

	// Check if progress record already exists
	var materialProgress models.StudyMaterialProgress
	err := p.db.Where("student_id = ? AND study_material_id = ?", studentID, progressData.StudyMaterialID).
		First(&materialProgress).Error

	if err == gorm.ErrRecordNotFound {
		// Create new progress record
		materialProgress = models.StudyMaterialProgress{
			StudentID:       studentID,
			StudyMaterialID: progressData.StudyMaterialID,
			LastReadAt:      time.Now(),
		}

		if err := p.db.Create(&materialProgress).Error; err != nil {
			duration := time.Since(start)
			slog.Error("UpdateStudyMaterialProgress failed - database error",
				"student_id", studentID,
				"study_material_id", progressData.StudyMaterialID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("failed to create study material progress: %w", err)
		}

		slog.Info("Created new study material progress record",
			"student_id", studentID,
			"study_material_id", progressData.StudyMaterialID,
			"progress_id", materialProgress.ID,
		)
	} else if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateStudyMaterialProgress failed - database error",
			"student_id", studentID,
			"study_material_id", progressData.StudyMaterialID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to query study material progress: %w", err)
	} else {
		// Update existing progress record
		materialProgress.LastReadAt = time.Now()

		if err := p.db.Save(&materialProgress).Error; err != nil {
			duration := time.Since(start)
			slog.Error("UpdateStudyMaterialProgress failed - database error",
				"student_id", studentID,
				"study_material_id", progressData.StudyMaterialID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("failed to update study material progress: %w", err)
		}

		slog.Info("Updated existing study material progress record",
			"student_id", studentID,
			"study_material_id", progressData.StudyMaterialID,
			"progress_id", materialProgress.ID,
		)
	}

	duration := time.Since(start)
	slog.Info("UpdateStudyMaterialProgress successful",
		"student_id", studentID,
		"study_material_id", progressData.StudyMaterialID,
		"last_read_at", materialProgress.LastReadAt,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}
