-- Migration: Add tile_image_url columns to courses and study_materials tables
-- Date: 2025-01-28
-- Description: Adds tile_image_url TEXT column to both courses and study_materials tables

-- Add tile_image_url column to courses table
ALTER TABLE courses ADD COLUMN IF NOT EXISTS tile_image_url TEXT;

-- Add tile_image_url column to study_materials table
ALTER TABLE study_materials ADD COLUMN IF NOT EXISTS tile_image_url TEXT;

-- Add comments for documentation
COMMENT ON COLUMN courses.tile_image_url IS 'URL for the course tile/thumbnail image';
COMMENT ON COLUMN study_materials.tile_image_url IS 'URL for the study material tile/thumbnail image';

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'courses'
AND column_name = 'tile_image_url';

SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'study_materials'
AND column_name = 'tile_image_url';
