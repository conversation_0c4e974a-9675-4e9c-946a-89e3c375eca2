package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestTestIsPaidFunctionality(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM courses_tests")
	db.Exec("DELETE FROM tests WHERE name LIKE 'Test IsPaid%'")
	db.Exec("DELETE FROM courses WHERE name LIKE 'Test IsPaid%'")
	db.Exec("DELETE FROM test_types WHERE name LIKE 'Test IsPaid%'")

	// Create a test type first
	testType := models.TestTypeForCreate{
		Name:             "Test IsPaid Type",
		SectionTypeNames: []string{}, // Empty for simplicity
	}
	testTypeResp := requestExecutionHelper(http.MethodPost, "/api/test-types", testType)
	assert.Equal(t, http.StatusOK, testTypeResp.Code)

	// Create a course
	course := models.CourseForCreate{
		Name:           "Test IsPaid Course",
		Description:    "Test course for IsPaid functionality",
		Price:          1000,
		Discount:       0,
		DurationInDays: 30,
		IsFree:         false,
		CourseType:     "IIT-JEE",
		SubjectNames:   []string{},
	}
	courseResp := requestExecutionHelper(http.MethodPost, "/api/courses", course)
	assert.Equal(t, http.StatusOK, courseResp.Code)

	var createdCourse models.SimpleEntityResponse
	err := json.Unmarshal(courseResp.Body.Bytes(), &createdCourse)
	assert.Nil(t, err)

	// Test 1: Create a paid test
	paidTest := models.TestForCreate{
		Name:         "Test IsPaid Paid Test",
		TestTypeName: "Test IsPaid Type",
		Description:  "A paid test",
		FromTime:     time.Now(),
		ToTime:       time.Now().Add(2 * time.Hour),
		IsPaid:       true,
	}
	paidTestResp := requestExecutionHelper(http.MethodPost, "/api/tests", paidTest)
	assert.Equal(t, http.StatusOK, paidTestResp.Code)

	var createdPaidTest models.SimpleEntityResponse
	err = json.Unmarshal(paidTestResp.Body.Bytes(), &createdPaidTest)
	assert.Nil(t, err)

	// Test 2: Create a free test
	freeTest := models.TestForCreate{
		Name:         "Test IsPaid Free Test",
		TestTypeName: "Test IsPaid Type",
		Description:  "A free test",
		FromTime:     time.Now(),
		ToTime:       time.Now().Add(2 * time.Hour),
		IsPaid:       false,
	}
	freeTestResp := requestExecutionHelper(http.MethodPost, "/api/tests", freeTest)
	assert.Equal(t, http.StatusOK, freeTestResp.Code)

	var createdFreeTest models.SimpleEntityResponse
	err = json.Unmarshal(freeTestResp.Body.Bytes(), &createdFreeTest)
	assert.Nil(t, err)

	// Test 3: Associate paid test with course (should succeed)
	associatePaidURL := fmt.Sprintf("/api/courses/%d/tests/%d", createdCourse.ID, createdPaidTest.ID)
	associatePaidResp := requestExecutionHelper(http.MethodPost, associatePaidURL, nil)
	assert.Equal(t, http.StatusOK, associatePaidResp.Code)

	// Test 4: Try to associate free test with course (should fail)
	associateFreeURL := fmt.Sprintf("/api/courses/%d/tests/%d", createdCourse.ID, createdFreeTest.ID)
	associateFreeResp := requestExecutionHelper(http.MethodPost, associateFreeURL, nil)
	assert.Equal(t, http.StatusInternalServerError, associateFreeResp.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(associateFreeResp.Body.Bytes(), &errorResponse)
	assert.Nil(t, err)
	assert.Contains(t, errorResponse["error"], "only paid tests can be associated with courses")

	// Test 5: Verify that GetTests returns IsPaid field
	getTestsResp := requestExecutionHelper(http.MethodGet, "/api/tests", nil)
	assert.Equal(t, http.StatusOK, getTestsResp.Code)

	var testsResponse map[string]interface{}
	err = json.Unmarshal(getTestsResp.Body.Bytes(), &testsResponse)
	assert.Nil(t, err)

	tests, ok := testsResponse["tests"].([]interface{})
	assert.True(t, ok)
	assert.Greater(t, len(tests), 0)

	// Find our created tests and verify IsPaid field
	foundPaidTest := false
	foundFreeTest := false
	for _, testInterface := range tests {
		test := testInterface.(map[string]interface{})
		testName := test["Name"].(string)
		
		if testName == "Test IsPaid Paid Test" {
			foundPaidTest = true
			assert.True(t, test["is_paid"].(bool))
		} else if testName == "Test IsPaid Free Test" {
			foundFreeTest = true
			assert.False(t, test["is_paid"].(bool))
		}
	}
	
	assert.True(t, foundPaidTest, "Should find the paid test in response")
	assert.True(t, foundFreeTest, "Should find the free test in response")

	// Clean up after test
	db.Exec("DELETE FROM courses_tests WHERE course_id = ?", createdCourse.ID)
	db.Exec("DELETE FROM tests WHERE name LIKE 'Test IsPaid%'")
	db.Exec("DELETE FROM courses WHERE name LIKE 'Test IsPaid%'")
	db.Exec("DELETE FROM test_types WHERE name LIKE 'Test IsPaid%'")
}
