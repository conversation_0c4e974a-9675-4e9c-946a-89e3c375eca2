-- Migration to add study_material_progresses table for tracking when students last read study materials
-- Run this script to update existing database schema

CREATE TABLE IF NOT EXISTS study_material_progresses (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL,
    study_material_id INTEGER NOT NULL,
    last_read_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    deleted_at TIMESTAMPTZ,
    
    -- Create unique constraint to ensure one progress record per student per study material
    CONSTRAINT unique_student_study_material UNIQUE (student_id, study_material_id),
    
    -- Foreign key constraints
    CONSTRAINT fk_study_material_progresses_student_id 
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_study_material_progresses_study_material_id 
        FOREIGN KEY (study_material_id) REFERENCES study_materials(id) ON DELETE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_student_id ON study_material_progresses(student_id);
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_study_material_id ON study_material_progresses(study_material_id);
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_last_read_at ON study_material_progresses(last_read_at);

-- Add comments for documentation
COMMENT ON TABLE study_material_progresses IS 'Tracks when study materials were last read by each student';
COMMENT ON COLUMN study_material_progresses.student_id IS 'Reference to the student who read the study material';
COMMENT ON COLUMN study_material_progresses.study_material_id IS 'Reference to the study material being read';
COMMENT ON COLUMN study_material_progresses.last_read_at IS 'When the student last read this study material';
