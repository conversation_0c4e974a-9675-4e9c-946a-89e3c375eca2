package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// CreateTransaction creates a new transaction with associated courses
func (p *DbPlugin) CreateTransaction(ctx context.Context, transaction *models.Transaction, courseIDs []uint) (*models.Transaction, error) {
	start := time.Now()
	slog.Info("Creating transaction",
		"student_id", transaction.StudentID,
		"amount", transaction.Amount,
		"course_count", len(courseIDs),
		"payment_method", transaction.PaymentMethod,
	)

	// Validate transaction status
	if transaction.Status != models.TransactionStatusPending &&
		transaction.Status != models.TransactionStatusCompleted &&
		transaction.Status != models.TransactionStatusFailed &&
		transaction.Status != models.TransactionStatusCancelled {
		return nil, fmt.Errorf("invalid transaction status '%s'", transaction.Status)
	}

	// Start database transaction
	tx := p.db.Begin()
	if tx.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to begin database transaction",
			"error", tx.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, tx.Error
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Validate that all courses exist and calculate total amount
	var courses []models.Course
	if err := tx.Where("id IN ?", courseIDs).Find(&courses).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to retrieve courses for transaction",
			"course_ids", courseIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	if len(courses) != len(courseIDs) {
		tx.Rollback()
		return nil, fmt.Errorf("some courses not found")
	}

	// Calculate total amount from courses
	var totalAmount int
	for _, course := range courses {
		if course.IsFree {
			tx.Rollback()
			return nil, fmt.Errorf("cannot create transaction for free course: %s", course.Name)
		}
		// Apply discount if any
		coursePrice := course.Price
		if course.Discount > 0 {
			coursePrice = int(float32(course.Price) * (1 - course.Discount/100))
		}
		totalAmount += coursePrice
	}

	// Update transaction amount
	transaction.Amount = totalAmount
	transaction.TransactionDate = time.Now()

	// Create the transaction
	if err := tx.Create(transaction).Error; err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to create transaction",
			"student_id", transaction.StudentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Associate courses with the transaction
	if err := tx.Model(transaction).Association("Courses").Append(courses); err != nil {
		tx.Rollback()
		duration := time.Since(start)
		slog.Error("Failed to associate courses with transaction",
			"transaction_id", transaction.ID,
			"course_ids", courseIDs,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit transaction",
			"transaction_id", transaction.ID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Load the complete transaction with associations
	if err := p.db.Preload("Student").Preload("Student.User").Preload("Courses").
		First(transaction, transaction.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load created transaction",
			"transaction_id", transaction.ID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Transaction created successfully",
		"transaction_id", transaction.ID,
		"student_id", transaction.StudentID,
		"amount", transaction.Amount,
		"course_count", len(courses),
		"duration_ms", duration.Milliseconds(),
	)

	return transaction, nil
}

// UpdateTransactionStatus updates the status of a transaction
func (p *DbPlugin) UpdateTransactionStatus(ctx context.Context, transactionID uint, status, paymentReference string) error {
	start := time.Now()
	slog.Info("Updating transaction status",
		"transaction_id", transactionID,
		"status", status,
		"payment_reference", paymentReference,
	)

	// Validate transaction status
	if status != models.TransactionStatusPending &&
		status != models.TransactionStatusCompleted &&
		status != models.TransactionStatusFailed &&
		status != models.TransactionStatusCancelled {
		return fmt.Errorf("invalid transaction status '%s'", status)
	}

	updates := map[string]interface{}{
		"status": status,
	}
	if paymentReference != "" {
		updates["payment_reference"] = paymentReference
	}

	result := p.db.Model(&models.Transaction{}).
		Where("id = ?", transactionID).
		Updates(updates)

	if result.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to update transaction status",
			"transaction_id", transactionID,
			"status", status,
			"error", result.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("transaction not found")
	}

	duration := time.Since(start)
	slog.Info("Transaction status updated successfully",
		"transaction_id", transactionID,
		"status", status,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}

// GetStudentTransactions retrieves all transactions for a student
func (p *DbPlugin) GetStudentTransactions(ctx context.Context, studentID uint) ([]models.Transaction, error) {
	start := time.Now()
	slog.Debug("Retrieving transactions for student", "student_id", studentID)

	var transactions []models.Transaction
	if err := p.db.Preload("Courses").
		Where("student_id = ?", studentID).
		Order("transaction_date DESC").
		Find(&transactions).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student transactions",
			"student_id", studentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Debug("Retrieved student transactions successfully",
		"student_id", studentID,
		"transaction_count", len(transactions),
		"duration_ms", duration.Milliseconds(),
	)

	return transactions, nil
}

// GetTransactionByID retrieves a specific transaction by ID
func (p *DbPlugin) GetTransactionByID(ctx context.Context, transactionID uint) (*models.Transaction, error) {
	start := time.Now()
	slog.Debug("Retrieving transaction by ID", "transaction_id", transactionID)

	var transaction models.Transaction
	if err := p.db.Preload("Student").Preload("Student.User").Preload("Courses").
		First(&transaction, transactionID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve transaction",
			"transaction_id", transactionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Debug("Retrieved transaction successfully",
		"transaction_id", transactionID,
		"student_id", transaction.StudentID,
		"duration_ms", duration.Milliseconds(),
	)

	return &transaction, nil
}

// GetCompletedTransactionsByStudent retrieves all completed transactions for a student
func (p *DbPlugin) GetCompletedTransactionsByStudent(ctx context.Context, studentID uint) ([]models.Transaction, error) {
	start := time.Now()
	slog.Debug("Retrieving completed transactions for student", "student_id", studentID)

	var transactions []models.Transaction
	if err := p.db.Preload("Courses").
		Where("student_id = ? AND status = ?", studentID, models.TransactionStatusCompleted).
		Order("transaction_date DESC").
		Find(&transactions).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve completed transactions",
			"student_id", studentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Debug("Retrieved completed transactions successfully",
		"student_id", studentID,
		"transaction_count", len(transactions),
		"duration_ms", duration.Milliseconds(),
	)

	return transactions, nil
}

// EnrollStudentInPurchasedCourses automatically enrolls a student in courses after successful payment
func (p *DbPlugin) EnrollStudentInPurchasedCourses(ctx context.Context, transactionID uint) error {
	start := time.Now()
	slog.Info("Auto-enrolling student in purchased courses", "transaction_id", transactionID)

	// Get the transaction with courses
	transaction, err := p.GetTransactionByID(ctx, transactionID)
	if err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve transaction for auto-enrollment",
			"transaction_id", transactionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return err
	}

	// Only enroll if transaction is completed
	if transaction.Status != models.TransactionStatusCompleted {
		return fmt.Errorf("cannot enroll: transaction status is %s", transaction.Status)
	}

	// Get user ID from student
	userID := transaction.Student.UserID

	// Enroll in each course
	var enrollmentErrors []string
	for _, course := range transaction.Courses {
		_, err := p.EnrollStudentInCourse(ctx, userID, course.ID)
		if err != nil {
			enrollmentErrors = append(enrollmentErrors, fmt.Sprintf("Course %s: %s", course.Name, err.Error()))
			slog.Error("Failed to auto-enroll student in course",
				"transaction_id", transactionID,
				"course_id", course.ID,
				"course_name", course.Name,
				"error", err.Error(),
			)
		} else {
			slog.Info("Successfully auto-enrolled student in course",
				"transaction_id", transactionID,
				"course_id", course.ID,
				"course_name", course.Name,
			)
		}
	}

	duration := time.Since(start)
	if len(enrollmentErrors) > 0 {
		slog.Error("Some auto-enrollments failed",
			"transaction_id", transactionID,
			"failed_courses", len(enrollmentErrors),
			"total_courses", len(transaction.Courses),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("enrollment errors: %v", enrollmentErrors)
	}

	slog.Info("Auto-enrollment completed successfully",
		"transaction_id", transactionID,
		"enrolled_courses", len(transaction.Courses),
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}
