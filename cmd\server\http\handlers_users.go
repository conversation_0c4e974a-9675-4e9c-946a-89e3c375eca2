package http

import (
	"net/http"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// UpdatePassword godoc
//
//		@Summary		UpdatePassword
//		@Description	update password for logged in user
//	     @Security       BearerAuth
//	 @Param			item	body	models.UpdatePassword	true	"new password"
//		@Tags			users
//		@Accept			json
//		@Produce		json
//		@Success		200
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/users/password [post]
func (h *Handlers) UpdatePassword(ctx *gin.Context) {
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	update := new(models.UpdatePassword)
	if err := ctx.ShouldBindJSON(update); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	err = h.db.UpdatePassword(ctx.Request.Context(), userID,
		update.NewPassword)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	ctx.JSON(http.StatusOK, struct{}{})
}
