package http

import (
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// CreateInstitution godoc
//
//	@Summary		Create Institution
//	@Description	create a new institution
//	@Description
//	@Description	Field Constraints:
//	@Description	- name: Institution name (required)
//	@Description	- city_or_town: City or town where institution is located (required)
//	@Description	- state: State where institution is located (required)
//	@Description	- contact_name: Name of the contact person (required)
//	@Description	- contact_number: Contact phone number (required)
//	@Security       BearerAuth
//	@Param			item	body	models.InstitutionForCreate	true	"institution details"
//	@Tags			institutions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.SimpleEntityResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/institutions [post]
func (h *Handlers) CreateInstitution(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("CreateInstitution request started", "client_ip", clientIP)

	institutionInput := new(models.InstitutionForCreate)
	if err := ctx.ShouldBindJSON(institutionInput); err != nil {
		duration := time.Since(start)
		slog.Warn("CreateInstitution failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	institution := &models.Institution{
		Name:          institutionInput.Name,
		CityOrTown:    institutionInput.CityOrTown,
		State:         institutionInput.State,
		ContactName:   institutionInput.ContactName,
		ContactNumber: institutionInput.ContactNumber,
	}

	createdInstitution, err := h.db.CreateInstitution(ctx.Request.Context(), institution)
	if err != nil {
		duration := time.Since(start)
		slog.Error("CreateInstitution failed - database error",
			"client_ip", clientIP,
			"name", institutionInput.Name,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("CreateInstitution successful",
		"client_ip", clientIP,
		"institution_id", createdInstitution.ID,
		"name", createdInstitution.Name,
		"duration_ms", duration.Milliseconds(),
	)

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdInstitution.ID,
		Name: createdInstitution.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// UpdateInstitution godoc
//
//	@Summary		Update Institution
//	@Description	update an existing institution
//	@Description
//	@Description	Field Constraints:
//	@Description	- name: Institution name (required)
//	@Description	- city_or_town: City or town where institution is located (required)
//	@Description	- state: State where institution is located (required)
//	@Description	- contact_name: Name of the contact person (required)
//	@Description	- contact_number: Contact phone number (required)
//	@Security       BearerAuth
//	@Param			id		path	int	true	"Institution ID"
//	@Param			item	body	models.InstitutionForUpdate	true	"institution details"
//	@Tags			institutions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.Institution
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/institutions/{id} [put]
func (h *Handlers) UpdateInstitution(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	institutionIDStr, ok := ctx.Params.Get("id")
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing institution ID parameter"})
		return
	}

	institutionID, err := strconv.ParseUint(institutionIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid institution ID"})
		return
	}

	slog.Info("UpdateInstitution request started",
		"client_ip", clientIP,
		"institution_id", institutionID,
	)

	institutionInput := new(models.InstitutionForUpdate)
	if err := ctx.ShouldBindJSON(institutionInput); err != nil {
		duration := time.Since(start)
		slog.Warn("UpdateInstitution failed - invalid request body",
			"client_ip", clientIP,
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	updatedInstitution, err := h.db.UpdateInstitution(ctx.Request.Context(), uint(institutionID), institutionInput)
	if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateInstitution failed - database error",
			"client_ip", clientIP,
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("UpdateInstitution successful",
		"client_ip", clientIP,
		"institution_id", institutionID,
		"name", updatedInstitution.Name,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, updatedInstitution)
}

// DeleteInstitution godoc
//
//	@Summary		Delete Institution
//	@Description	delete an institution (soft delete)
//	@Security       BearerAuth
//	@Param			id	path	int	true	"Institution ID"
//	@Tags			institutions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	map[string]string
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/institutions/{id} [delete]
func (h *Handlers) DeleteInstitution(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	institutionIDStr, ok := ctx.Params.Get("id")
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing institution ID parameter"})
		return
	}

	institutionID, err := strconv.ParseUint(institutionIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid institution ID"})
		return
	}

	slog.Info("DeleteInstitution request started",
		"client_ip", clientIP,
		"institution_id", institutionID,
	)

	err = h.db.DeleteInstitution(ctx.Request.Context(), uint(institutionID))
	if err != nil {
		duration := time.Since(start)
		slog.Error("DeleteInstitution failed - database error",
			"client_ip", clientIP,
			"institution_id", institutionID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("DeleteInstitution successful",
		"client_ip", clientIP,
		"institution_id", institutionID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"message": "Institution deleted successfully"})
}

// GetInstitutions godoc
//
//	@Summary		Get Institutions
//	@Description	get all institutions
//	@Security       BearerAuth
//	@Tags			institutions
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	[]models.Institution
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/institutions [get]
func (h *Handlers) GetInstitutions(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetInstitutions request started", "client_ip", clientIP)

	institutions, err := h.db.GetInstitutions(ctx.Request.Context())
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetInstitutions failed - database error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetInstitutions successful",
		"client_ip", clientIP,
		"institution_count", len(institutions),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, institutions)
}
