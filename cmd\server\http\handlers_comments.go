package http

import (
	"log/slog"
	"net/http"
	"strconv"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// AddComment godoc
//
//	@Summary		Add Comment
//	@Description	Add a comment to a video or study material
//	@Security       BearerAuth
//	@Param			item	body	models.CommentForCreate	true	"comment details"
//	@Tags			comments
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.Comment
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/comments [post]
func (h *Handlers) AddComment(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("AddComment request started",
		"client_ip", clientIP,
		"method", ctx.Request.Method,
		"path", ctx.Request.URL.Path,
	)

	// Extract user ID from JWT token
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("AddComment failed - token extraction error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse request body
	commentInput := new(models.CommentForCreate)
	if err := ctx.ShouldBindJSON(commentInput); err != nil {
		duration := time.Since(start)
		slog.Warn("AddComment failed - invalid request body",
			"client_ip", clientIP,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate that either video_id or material_id is provided, but not both
	if (commentInput.VideoID == nil && commentInput.MaterialID == nil) ||
		(commentInput.VideoID != nil && commentInput.MaterialID != nil) {
		duration := time.Since(start)
		slog.Warn("AddComment failed - invalid target specification",
			"client_ip", clientIP,
			"user_id", userID,
			"video_id", commentInput.VideoID,
			"material_id", commentInput.MaterialID,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Must specify either video_id or material_id, but not both"})
		return
	}

	// Create comment model
	comment := &models.Comment{
		UserID:      userID,
		VideoID:     commentInput.VideoID,
		MaterialID:  commentInput.MaterialID,
		CommentText: commentInput.CommentText,
	}

	// Add comment to database
	createdComment, err := h.db.AddComment(ctx.Request.Context(), comment)
	if err != nil {
		duration := time.Since(start)
		slog.Error("AddComment failed - database error",
			"client_ip", clientIP,
			"user_id", userID,
			"video_id", commentInput.VideoID,
			"material_id", commentInput.MaterialID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("AddComment completed successfully",
		"client_ip", clientIP,
		"user_id", userID,
		"comment_id", createdComment.ID,
		"video_id", commentInput.VideoID,
		"material_id", commentInput.MaterialID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, createdComment)
}

// AddResponse godoc
//
//	@Summary		Add Response
//	@Description	Add a response to an existing comment
//	@Security       BearerAuth
//	@Param			item	body	models.ResponseForCreate	true	"response details"
//	@Tags			comments
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.Response
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/responses [post]
func (h *Handlers) AddResponse(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("AddResponse request started",
		"client_ip", clientIP,
		"method", ctx.Request.Method,
		"path", ctx.Request.URL.Path,
	)

	// Extract user ID from JWT token
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("AddResponse failed - token extraction error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse request body
	responseInput := new(models.ResponseForCreate)
	if err := ctx.ShouldBindJSON(responseInput); err != nil {
		duration := time.Since(start)
		slog.Warn("AddResponse failed - invalid request body",
			"client_ip", clientIP,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create response model
	response := &models.Response{
		CommentID:    responseInput.CommentID,
		UserID:       userID,
		ResponseText: responseInput.ResponseText,
	}

	// Add response to database
	createdResponse, err := h.db.AddResponse(ctx.Request.Context(), response)
	if err != nil {
		duration := time.Since(start)
		slog.Error("AddResponse failed - database error",
			"client_ip", clientIP,
			"user_id", userID,
			"comment_id", responseInput.CommentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("AddResponse completed successfully",
		"client_ip", clientIP,
		"user_id", userID,
		"response_id", createdResponse.ID,
		"comment_id", responseInput.CommentID,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, createdResponse)
}

// GetComments godoc
//
//	@Summary		Get Comments
//	@Description	Get comments for a video or study material. For students, returns only their comments. For admins, returns all comments.
//	@Security       BearerAuth
//	@Param			video_id	query		uint	false	"Video ID"
//	@Param			material_id	query		uint	false	"Study Material ID"
//	@Tags			comments
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.CommentsResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/comments [get]
func (h *Handlers) GetComments(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetComments request started",
		"client_ip", clientIP,
		"method", ctx.Request.Method,
		"path", ctx.Request.URL.Path,
	)

	// Extract user ID from JWT token
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetComments failed - token extraction error",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Parse query parameters
	videoIDStr := ctx.Query("video_id")
	materialIDStr := ctx.Query("material_id")

	// Validate that either video_id or material_id is provided, but not both
	if (videoIDStr == "" && materialIDStr == "") || (videoIDStr != "" && materialIDStr != "") {
		duration := time.Since(start)
		slog.Warn("GetComments failed - invalid query parameters",
			"client_ip", clientIP,
			"user_id", userID,
			"video_id", videoIDStr,
			"material_id", materialIDStr,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Must specify either video_id or material_id, but not both"})
		return
	}

	var commentsResponse *models.CommentsResponse

	if videoIDStr != "" {
		// Get comments for video
		videoID, err := strconv.ParseUint(videoIDStr, 10, 32)
		if err != nil {
			duration := time.Since(start)
			slog.Warn("GetComments failed - invalid video_id",
				"client_ip", clientIP,
				"user_id", userID,
				"video_id", videoIDStr,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid video_id"})
			return
		}

		commentsResponse, err = h.db.GetCommentsForVideo(ctx.Request.Context(), uint(videoID), userID)
		if err != nil {
			duration := time.Since(start)
			slog.Error("GetComments failed - database error for video",
				"client_ip", clientIP,
				"user_id", userID,
				"video_id", videoID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	} else {
		// Get comments for study material
		materialID, err := strconv.ParseUint(materialIDStr, 10, 32)
		if err != nil {
			duration := time.Since(start)
			slog.Warn("GetComments failed - invalid material_id",
				"client_ip", clientIP,
				"user_id", userID,
				"material_id", materialIDStr,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid material_id"})
			return
		}

		commentsResponse, err = h.db.GetCommentsForMaterial(ctx.Request.Context(), uint(materialID), userID)
		if err != nil {
			duration := time.Since(start)
			slog.Error("GetComments failed - database error for material",
				"client_ip", clientIP,
				"user_id", userID,
				"material_id", materialID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
	}

	duration := time.Since(start)
	slog.Info("GetComments completed successfully",
		"client_ip", clientIP,
		"user_id", userID,
		"video_id", videoIDStr,
		"material_id", materialIDStr,
		"comment_count", len(commentsResponse.Comments),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, commentsResponse)
}
