#!/bin/bash

LOG_FILE="run_commands.log"

# Clear previous log
> "$LOG_FILE"

# Log with timestamp
log() {
    local MESSAGE="$1"
    echo "$(date '+%Y-%m-%d %H:%M:%S') | $MESSAGE" | tee -a "$LOG_FILE"
}

# Run a command silently unless it fails
run_command() {
    local CMD="$1"
    log "Running: $CMD"

    # Temporary file to capture command output
    local OUTPUT
    OUTPUT=$(mktemp)

    # Run the command, redirecting both stdout and stderr
    bash -c "$CMD" >"$OUTPUT" 2>&1
    local STATUS=$?

    if [ $STATUS -eq 0 ]; then
        log "✅ Success: $CMD"
        rm -f "$OUTPUT"
    else
        log "❌ Failed: $CMD (exit code $STATUS)"
        log "----- Output Start -----"
        cat "$OUTPUT" | tee -a "$LOG_FILE"
        log "----- Output End -----"
        rm -f "$OUTPUT"
        exit $STATUS
    fi
}

# Run commands
run_command "golangci-lint run"
run_command "go test ./..."
run_command "swag init --parseDependency --parseInternal -g cmd/server/http/http.go"
run_command "sed -i -e 's/localhost/************/g' docs/*"
run_command "go mod tidy"
run_command "go build"
run_command "GOOS=linux GOARCH=amd64 go build -o ziaacademy-backend"
run_command "echo 'All done!'"