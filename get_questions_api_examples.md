# Get Questions API - Updated with Optional Filters

## Overview
The Get Questions API has been enhanced to accept optional filters for subject, chapter, topic, and difficulty. All parameters are now optional, allowing for flexible querying.

## Endpoint
```
GET /api/questions
```

## Query Parameters (All Optional)
- `subject` (string, optional): Filter by subject name
- `chapter` (string, optional): Filter by chapter name  
- `topic` (string, optional): Filter by topic name
- `difficulty` (string, optional): Filter by difficulty level

## Usage Examples

### 1. Get All Questions
```
GET /api/questions
```
Returns all questions in the system.

### 2. Filter by Subject Only
```
GET /api/questions?subject=Physics
```
Returns all questions from the Physics subject.

### 3. Filter by Subject and Chapter
```
GET /api/questions?subject=Physics&chapter=Mechanics
```
Returns all questions from the Mechanics chapter in Physics.

### 4. Filter by Subject, Chapter, and Topic
```
GET /api/questions?subject=Physics&chapter=Mechanics&topic=Newton's Laws
```
Returns all questions from the Newton's Laws topic in Mechanics chapter of Physics.

### 5. Filter by Subject, Chapter, Topic, and Difficulty
```
GET /api/questions?subject=Physics&chapter=Mechanics&topic=Newton's Laws&difficulty=Easy
```
Returns easy questions from Newton's Laws topic in Mechanics chapter of Physics.

### 6. Filter by Difficulty Only
```
GET /api/questions?difficulty=Hard
```
Returns all hard questions across all subjects.

### 7. Filter by Topic and Difficulty (Mixed Filters)
```
GET /api/questions?topic=Organic Chemistry&difficulty=Medium
```
Returns medium difficulty questions from Organic Chemistry topic.

## Response Format
```json
{
  "questions": [
    {
      "id": 1,
      "text": "What is Newton's first law?",
      "questionType": "mcq",
      "imageUrl": "",
      "fileUrl": "",
      "correctAnswer": "",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "topic": {
        "id": 1,
        "name": "Newton's Laws",
        "chapter": {
          "id": 1,
          "name": "Mechanics",
          "subject": {
            "id": 1,
            "name": "Physics",
            "displayName": "Physics"
          }
        }
      },
      "difficulty": {
        "id": 1,
        "name": "Easy"
      },
      "options": [
        {
          "id": 1,
          "optionText": "An object at rest stays at rest",
          "isCorrect": true
        }
      ]
    }
  ]
}
```

## Key Features
- **Flexible Filtering**: Use any combination of the four optional parameters
- **Hierarchical Relationships**: Questions include full hierarchy (Subject → Chapter → Topic → Question)
- **Complete Data**: Response includes options for MCQ questions and all related entities
- **Performance Optimized**: Uses efficient database joins and preloading

## Database Relationships
The API leverages the following hierarchy:
```
Subject (1:N) Chapter (1:N) Topic (1:N) Question (N:1) Difficulty
```

This allows for efficient filtering at any level of the hierarchy.
