package http

import (
	"fmt"
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// GetContent godoc
//
//	@Summary		Get Content
//	@Description	get videos and study materials organized by subjects with progress information. For students, also returns recently accessed content organized by subjects, with videos and PDFs sorted by last accessed timestamps.
//	@Security       BearerAuth
//	@Param			course_id	query		int	false	"Course ID (optional - if not provided, returns content for all courses)"
//	@Tags			content
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.ContentResponseWithProgress
//	@Failure		400	{object}	HTTPError
//	@Failure		401	{object}	HTTPError
//	@Failure		404	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/content [get]
func (h *Handlers) GetContent(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Get student ID from JWT token
	userID, exists := ctx.Get("userID")
	if !exists {
		slog.Error("GetContent failed - userID not found in context",
			"client_ip", clientIP,
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get user ID (convert from float64 to uint)
	userIDFloat, ok := userID.(float64)
	if !ok {
		slog.Error("GetContent failed - invalid userID type",
			"client_ip", clientIP,
			"userID_type", fmt.Sprintf("%T", userID),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
		return
	}

	userIDUint := uint(userIDFloat)

	// Check if the user is a student by getting their role
	user, err := h.db.GetUserByID(ctx.Request.Context(), userIDUint)
	if err != nil {
		slog.Error("GetContent failed - failed to get user",
			"client_ip", clientIP,
			"user_id", userIDUint,
			"error", err.Error(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user information"})
		return
	}

	isStudent := strings.ToLower(user.Role) == "student"
	var studentID uint

	if isStudent {
		// Get student ID from user ID for students
		studentID, err = h.db.GetStudentIDByUserID(ctx.Request.Context(), userIDUint)
		if err != nil {
			duration := time.Since(start)
			slog.Error("GetContent failed - student not found",
				"client_ip", clientIP,
				"user_id", userIDUint,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusNotFound, gin.H{"error": "Student not found"})
			return
		}
	} else {
		// For non-students, use the user ID as student ID (for compatibility)
		studentID = userIDUint
	}

	courseIDStr := ctx.Query("course_id")
	var courseID *uint
	if courseIDStr != "" {
		courseIDInt, err := strconv.Atoi(courseIDStr)
		if err != nil {
			slog.Error("GetContent failed - invalid course_id parameter",
				"client_ip", clientIP,
				"course_id", courseIDStr,
				"error", err.Error(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid course_id parameter"})
			return
		}
		courseIDUint := uint(courseIDInt)
		courseID = &courseIDUint
	}

	slog.Info("GetContent request started",
		"client_ip", clientIP,
		"user_id", userIDUint,
		"user_role", user.Role,
		"is_student", isStudent,
		"student_id", studentID,
		"course_id", courseID,
	)

	content, err := h.db.GetContentBySubjectsWithProgress(ctx.Request.Context(), studentID, courseID, isStudent)
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetContent failed - database error",
			"client_ip", clientIP,
			"student_id", studentID,
			"course_id", courseID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("GetContent successful",
		"client_ip", clientIP,
		"student_id", studentID,
		"course_id", courseID,
		"subject_count", len(content.Subjects),
		"total_videos", content.Summary.TotalVideos,
		"total_materials", content.Summary.TotalPdfs,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, content)
}

// AddVideo godoc
//
//		@Summary	    AddVideo
//		@Description	add a new video
//	     @Security       BearerAuth
//	 @Param			item	body	models.VideoForCreate	true	"video details"
//		@Tags			library
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/videos [post]
func (h *Handlers) AddVideo(ctx *gin.Context) {
	videoInput := new(models.VideoForCreate)
	if err := ctx.ShouldBindJSON(videoInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	video := &models.Video{
		Name:        videoInput.Name,
		DisplayName: videoInput.DisplayName,
		VideoUrl:    videoInput.VideoUrl,
		ViewCount:   0, // Default to 0
	}

	createdVideo, err := h.db.AddVideo(ctx.Request.Context(), video, videoInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdVideo.ID,
		Name: createdVideo.Name,
	}
	ctx.JSON(http.StatusOK, response)
}

// AddStudyMaterial godoc
//
//		@Summary	    AddStudyMaterial
//		@Description	add a new material
//	     @Security       BearerAuth
//	 @Param			item	body	models.MaterialForCreate	true	"material details"
//		@Tags			library
//		@Accept			json
//		@Produce		json
//		@Success		200	{object}	models.SimpleEntityResponse
//		@Failure		400	{object}	HTTPError
//		@Failure		404	{object}	HTTPError
//		@Failure		500	{object}	HTTPError
//		@Router			/studymaterials [post]
//
// AddStudyMaterial is the HTTP handler to add a new pdf to a chapter
func (h *Handlers) AddStudyMaterial(ctx *gin.Context) {
	materialInput := new(models.MaterialForCreate)
	if err := ctx.ShouldBindJSON(materialInput); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	material := &models.StudyMaterial{
		Name:         materialInput.Name,
		DisplayName:  materialInput.DisplayName,
		Url:          materialInput.Url,
		TileImageUrl: materialInput.TileImageUrl,
	}

	createdMaterial, err := h.db.AddStudyMaterial(ctx.Request.Context(), material, materialInput.ChapterName)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Return only ID and name
	response := models.SimpleEntityResponse{
		ID:   createdMaterial.ID,
		Name: createdMaterial.Name,
	}
	ctx.JSON(http.StatusOK, response)
}
