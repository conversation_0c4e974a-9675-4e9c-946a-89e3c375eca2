package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestToggleTestResultsDisclosure(t *testing.T) {
	// Clean up before test
	testName := "Results Disclosure Test"
	testTypeName := "Results Disclosure Test Type"
	subjectName := "Test Subject for Results Disclosure"
	sectionTypeName := "Test Section Type for Results Disclosure"

	// Clean up in proper order
	db.Exec("DELETE FROM sections WHERE test_id IN (SELECT id FROM tests WHERE name = ?)", testName)
	db.Exec("DELETE FROM tests WHERE name = ?", testName)
	db.Exec("DELETE FROM test_type_section_types WHERE test_type_id IN (SELECT id FROM test_types WHERE name = ?)", testTypeName)
	db.Exec("DELETE FROM test_types WHERE name = ?", testTypeName)
	db.Exec("DELETE FROM section_types WHERE name = ?", sectionTypeName)
	db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)

	// Step 1: Create a subject
	subject := models.Subject{
		Name:        subjectName,
		DisplayName: subjectName,
	}
	subjectResp := requestExecutionHelper(http.MethodPost, "/api/subjects", subject)
	assert.Equal(t, http.StatusOK, subjectResp.Code)

	// Step 2: Create a section type
	sectionType := models.SectionTypeForCreate{
		Name:          sectionTypeName,
		SubjectName:   subjectName,
		QuestionCount: 10,
		PositiveMarks: 4.0,
		NegativeMarks: 1.0,
	}
	sectionTypeResp := requestExecutionHelper(http.MethodPost, "/api/section-types", sectionType)
	assert.Equal(t, http.StatusOK, sectionTypeResp.Code)

	// Step 3: Create a test type
	testType := models.TestTypeForCreate{
		Name:             testTypeName,
		SectionTypeNames: []string{sectionTypeName},
	}
	testTypeResp := requestExecutionHelper(http.MethodPost, "/api/test-types", testType)
	assert.Equal(t, http.StatusOK, testTypeResp.Code)

	// Step 4: Create a test
	testForCreate := models.TestForCreate{
		Name:         testName,
		TestTypeName: testTypeName,
		Description:  "Test for results disclosure functionality",
	}
	testResp := requestExecutionHelper(http.MethodPost, "/api/tests", testForCreate)
	assert.Equal(t, http.StatusOK, testResp.Code)

	var createdTestResponse models.SimpleEntityResponse
	err := json.Unmarshal(testResp.Body.Bytes(), &createdTestResponse)
	assert.Nil(t, err)

	// Step 5: Verify initial state (results_disclosed should be false by default)
	getTestsResp := requestExecutionHelper(http.MethodGet, "/api/tests", nil)
	assert.Equal(t, http.StatusOK, getTestsResp.Code)

	var testsResponse struct {
		Tests []models.TestForGet `json:"tests"`
	}
	err = json.Unmarshal(getTestsResp.Body.Bytes(), &testsResponse)
	assert.Nil(t, err)

	tests := testsResponse.Tests

	// Find our test
	var ourTest *models.TestForGet
	for i := range tests {
		if tests[i].ID == createdTestResponse.ID {
			ourTest = &tests[i]
			break
		}
	}
	assert.NotNil(t, ourTest, "Should find the created test")
	assert.False(t, ourTest.ResultsDisclosed, "Results should not be disclosed by default")

	// Step 6: Toggle results disclosure to true
	toggleURL := fmt.Sprintf("/api/tests/%d/results-disclosure", createdTestResponse.ID)
	toggleResp := requestExecutionHelper(http.MethodPut, toggleURL, nil)
	assert.Equal(t, http.StatusOK, toggleResp.Code)

	var toggleResult map[string]string
	err = json.Unmarshal(toggleResp.Body.Bytes(), &toggleResult)
	assert.Nil(t, err)
	assert.Equal(t, "Test results disclosure status toggled successfully", toggleResult["message"])

	// Step 7: Verify results disclosure is now true
	getTestsResp2 := requestExecutionHelper(http.MethodGet, "/api/tests", nil)
	assert.Equal(t, http.StatusOK, getTestsResp2.Code)

	var testsResponse2 struct {
		Tests []models.TestForGet `json:"tests"`
	}
	err = json.Unmarshal(getTestsResp2.Body.Bytes(), &testsResponse2)
	assert.Nil(t, err)

	tests2 := testsResponse2.Tests

	// Find our test again
	var ourTest2 *models.TestForGet
	for i := range tests2 {
		if tests2[i].ID == createdTestResponse.ID {
			ourTest2 = &tests2[i]
			break
		}
	}
	assert.NotNil(t, ourTest2, "Should find the created test")
	assert.True(t, ourTest2.ResultsDisclosed, "Results should now be disclosed")

	// Step 8: Toggle results disclosure back to false
	toggleResp2 := requestExecutionHelper(http.MethodPut, toggleURL, nil)
	assert.Equal(t, http.StatusOK, toggleResp2.Code)

	// Step 9: Verify results disclosure is now false again
	getTestsResp3 := requestExecutionHelper(http.MethodGet, "/api/tests", nil)
	assert.Equal(t, http.StatusOK, getTestsResp3.Code)

	var testsResponse3 struct {
		Tests []models.TestForGet `json:"tests"`
	}
	err = json.Unmarshal(getTestsResp3.Body.Bytes(), &testsResponse3)
	assert.Nil(t, err)

	tests3 := testsResponse3.Tests

	// Find our test one more time
	var ourTest3 *models.TestForGet
	for i := range tests3 {
		if tests3[i].ID == createdTestResponse.ID {
			ourTest3 = &tests3[i]
			break
		}
	}
	assert.NotNil(t, ourTest3, "Should find the created test")
	assert.False(t, ourTest3.ResultsDisclosed, "Results should be back to not disclosed")
}
