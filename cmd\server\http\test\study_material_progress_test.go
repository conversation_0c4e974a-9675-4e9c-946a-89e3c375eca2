package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestStudyMaterialProgressAPI(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping study material progress API test")
		return
	}

	// Clean up before test
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testSubjectName := "Study Material Progress Test Subject " + timestamp
	testChapterName := "Study Material Progress Test Chapter " + timestamp
	testMaterialName := "test_material_progress_" + timestamp

	// Create test data
	subject := models.Subject{
		Name:        testSubjectName,
		DisplayName: "Study Material Progress Test Subject",
	}
	db.Create(&subject)

	chapter := models.Chapter{
		Name:        testChapterName,
		DisplayName: "Study Material Progress Test Chapter",
		SubjectID:   subject.ID,
	}
	db.Create(&chapter)

	studyMaterial := models.StudyMaterial{
		Name:        testMaterialName,
		DisplayName: "Test Study Material Progress",
		Url:         "https://example.com/test-material.pdf",
		ChapterID:   chapter.ID,
	}
	db.Create(&studyMaterial)

	// Create test student with authentication token
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Test Student Material Progress",
			Email:          "test.material.progress." + timestamp + "@example.com",
			PhoneNumber:    "1234567890" + timestamp[len(timestamp)-3:],
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543210",
		ParentEmail: "parent.material.progress." + timestamp + "@example.com",
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Test City",
		State:       "Test State",
		Password:    "testpassword123",
	}

	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)

	studentToken := studentResponse.Token
	assert.NotEmpty(t, studentToken)

	testStudent := studentResponse.Student
	studentID := testStudent.ID

	// Clean up after test
	defer func() {
		// Cleanup study material progress records
		db.Exec("DELETE FROM study_material_progresses WHERE study_material_id = ?", studyMaterial.ID)
		db.Exec("DELETE FROM study_materials WHERE id = ?", studyMaterial.ID)
		db.Exec("DELETE FROM chapters WHERE id = ?", chapter.ID)
		db.Exec("DELETE FROM subjects WHERE id = ?", subject.ID)

		// Get the user ID from the student record before deleting
		var studentRecord models.Student
		db.First(&studentRecord, testStudent.ID)
		db.Exec("DELETE FROM students WHERE id = ?", testStudent.ID)
		db.Exec("DELETE FROM users WHERE id = ?", studentRecord.UserID)
	}()

	// Test 1: Update study material progress
	t.Run("UpdateStudyMaterialProgress", func(t *testing.T) {
		progressData := models.StudyMaterialProgressForUpdate{
			StudyMaterialID: studyMaterial.ID,
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/study-material-progress", progressData, studentToken)

		assert.Equal(t, http.StatusOK, resp.Code)

		var response map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		assert.Nil(t, err)
		assert.Equal(t, "Study material progress updated successfully", response["message"])
	})

	// Test 2: Verify progress is saved in database
	t.Run("VerifyProgressInDatabase", func(t *testing.T) {
		var progress models.StudyMaterialProgress
		err := db.Where("student_id = ? AND study_material_id = ?", studentID, studyMaterial.ID).First(&progress).Error
		assert.Nil(t, err)
		assert.Equal(t, studentID, progress.StudentID)
		assert.Equal(t, studyMaterial.ID, progress.StudyMaterialID)
		assert.WithinDuration(t, time.Now(), progress.LastReadAt, time.Minute)
	})

	// Test 3: Update progress again (should update existing record)
	t.Run("UpdateExistingProgress", func(t *testing.T) {
		// Wait a moment to ensure different timestamp
		time.Sleep(time.Second)

		progressData := models.StudyMaterialProgressForUpdate{
			StudyMaterialID: studyMaterial.ID,
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/study-material-progress", progressData, studentToken)

		assert.Equal(t, http.StatusOK, resp.Code)

		// Verify only one record exists and it's updated
		var progressRecords []models.StudyMaterialProgress
		err := db.Where("student_id = ? AND study_material_id = ?", studentID, studyMaterial.ID).Find(&progressRecords).Error
		assert.Nil(t, err)
		assert.Equal(t, 1, len(progressRecords))
	})

	// Test 4: Verify progress integration in content API
	t.Run("GetContentWithStudyMaterialProgress", func(t *testing.T) {
		resp := authenticatedRequestHelper(http.MethodGet, "/api/content", nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content models.ContentResponseWithProgress
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		assert.GreaterOrEqual(t, len(content.Subjects), 1, "Should have at least one subject")

		// Find our test subject
		testSubject, exists := content.Subjects[testSubjectName]
		assert.True(t, exists, "Test subject should exist in content response")

		assert.NotNil(t, testSubject, "Test subject should be found in content response")
		assert.GreaterOrEqual(t, len(testSubject.Pdfs), 1, "Test subject should have at least one PDF")

		// Find our test material and verify it has progress
		var testMaterial *models.StudyMaterialForGet
		for _, material := range testSubject.Pdfs {
			if material.Name == testMaterialName {
				testMaterial = &material
				break
			}
		}

		assert.NotNil(t, testMaterial, "Test material should be found in content response")
		if testMaterial.LastReadAt != nil {
			assert.WithinDuration(t, time.Now(), *testMaterial.LastReadAt, time.Minute*5)
		} else {
			t.Log("Test material does not have last_read_at timestamp - this is expected if no progress was recorded")
		}
	})

	// Test 5: Test with invalid study material ID
	t.Run("UpdateProgressWithInvalidMaterialID", func(t *testing.T) {
		progressData := models.StudyMaterialProgressForUpdate{
			StudyMaterialID: 99999, // Non-existent ID
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/study-material-progress", progressData, studentToken)

		assert.Equal(t, http.StatusInternalServerError, resp.Code)
	})

	// Test 6: Test with missing study material ID
	t.Run("UpdateProgressWithMissingMaterialID", func(t *testing.T) {
		progressData := map[string]interface{}{} // Empty data

		resp := authenticatedRequestHelper(http.MethodPut, "/api/study-material-progress", progressData, studentToken)

		assert.Equal(t, http.StatusBadRequest, resp.Code)
	})
}
