package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"

	"gorm.io/gorm"
)

// UpdateVideoProgress updates or creates video progress for a student
func (p *DbPlugin) UpdateVideoProgress(ctx context.Context, studentID uint, progressData *models.VideoProgressForUpdate) error {
	start := time.Now()
	slog.Info("Updating video progress",
		"student_id", studentID,
		"video_id", progressData.VideoID,
		"progress_seconds", progressData.ProgressSeconds,
		"progress_percent", progressData.ProgressPercent,
	)

	// Verify that the video exists
	var video models.Video
	if err := p.db.First(&video, progressData.VideoID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("UpdateVideoProgress failed - video not found",
			"student_id", studentID,
			"video_id", progressData.VideoID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("video with ID %d not found: %w", progressData.VideoID, err)
	}

	// Verify that the student exists
	var student models.Student
	if err := p.db.First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("UpdateVideoProgress failed - student not found",
			"student_id", studentID,
			"video_id", progressData.VideoID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("student with ID %d not found: %w", studentID, err)
	}

	// Update or create video progress
	var videoProgress models.VideoProgress
	err := p.db.Where("student_id = ? AND video_id = ?", studentID, progressData.VideoID).First(&videoProgress).Error

	if err == gorm.ErrRecordNotFound {
		// Create new progress record
		videoProgress = models.VideoProgress{
			StudentID:       studentID,
			VideoID:         progressData.VideoID,
			ProgressSeconds: progressData.ProgressSeconds,
			ProgressPercent: progressData.ProgressPercent,
			VideoDuration:   progressData.VideoDuration,
			IsCompleted:     progressData.IsCompleted,
			LastWatchedAt:   time.Now(),
		}

		if err := p.db.Create(&videoProgress).Error; err != nil {
			duration := time.Since(start)
			slog.Error("UpdateVideoProgress failed - database error",
				"student_id", studentID,
				"video_id", progressData.VideoID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("failed to create video progress: %w", err)
		}

		slog.Info("Created new video progress record",
			"student_id", studentID,
			"video_id", progressData.VideoID,
			"progress_id", videoProgress.ID,
		)
	} else if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateVideoProgress failed - database error",
			"student_id", studentID,
			"video_id", progressData.VideoID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return fmt.Errorf("failed to query video progress: %w", err)
	} else {
		// Update existing progress record
		videoProgress.ProgressSeconds = progressData.ProgressSeconds
		videoProgress.ProgressPercent = progressData.ProgressPercent
		videoProgress.VideoDuration = progressData.VideoDuration
		videoProgress.IsCompleted = progressData.IsCompleted
		videoProgress.LastWatchedAt = time.Now()

		if err := p.db.Save(&videoProgress).Error; err != nil {
			duration := time.Since(start)
			slog.Error("UpdateVideoProgress failed - database error",
				"student_id", studentID,
				"video_id", progressData.VideoID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return fmt.Errorf("failed to update video progress: %w", err)
		}

		slog.Info("Updated existing video progress record",
			"student_id", studentID,
			"video_id", progressData.VideoID,
			"progress_id", videoProgress.ID,
		)
	}

	// Increment video view count if this is a new view or significant progress
	if err == gorm.ErrRecordNotFound || progressData.ProgressPercent > 10 {
		if err := p.db.Model(&video).Update("view_count", gorm.Expr("view_count + ?", 1)).Error; err != nil {
			slog.Warn("Failed to update video view count",
				"video_id", progressData.VideoID,
				"error", err.Error(),
			)
		}
	}

	duration := time.Since(start)
	slog.Info("Video progress updated successfully",
		"student_id", studentID,
		"video_id", progressData.VideoID,
		"progress_percent", progressData.ProgressPercent,
		"is_completed", progressData.IsCompleted,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}
