
<!DOCTYPE html>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
		<title>http: Go Coverage Report</title>
		<style>
			body {
				background: black;
				color: rgb(80, 80, 80);
			}
			body, pre, #legend span {
				font-family: Menlo, monospace;
				font-weight: bold;
			}
			#topbar {
				background: black;
				position: fixed;
				top: 0; left: 0; right: 0;
				height: 42px;
				border-bottom: 1px solid rgb(80, 80, 80);
			}
			#content {
				margin-top: 50px;
			}
			#nav, #legend {
				float: left;
				margin-left: 10px;
			}
			#legend {
				margin-top: 12px;
			}
			#nav {
				margin-top: 10px;
			}
			#legend span {
				margin: 0 5px;
			}
			.cov0 { color: rgb(192, 0, 0) }
.cov1 { color: rgb(128, 128, 128) }
.cov2 { color: rgb(116, 140, 131) }
.cov3 { color: rgb(104, 152, 134) }
.cov4 { color: rgb(92, 164, 137) }
.cov5 { color: rgb(80, 176, 140) }
.cov6 { color: rgb(68, 188, 143) }
.cov7 { color: rgb(56, 200, 146) }
.cov8 { color: rgb(44, 212, 149) }
.cov9 { color: rgb(32, 224, 152) }
.cov10 { color: rgb(20, 236, 155) }

		</style>
	</head>
	<body>
		<div id="topbar">
			<div id="nav">
				<select id="files">
				
				<option value="file0">ziaacademy-backend/cmd/server/http/handlers_admins.go (55.0%)</option>
				
				<option value="file1">ziaacademy-backend/cmd/server/http/handlers_chapters.go (0.0%)</option>
				
				<option value="file2">ziaacademy-backend/cmd/server/http/handlers_courses.go (0.0%)</option>
				
				<option value="file3">ziaacademy-backend/cmd/server/http/handlers_login.go (0.0%)</option>
				
				<option value="file4">ziaacademy-backend/cmd/server/http/handlers_questions.go (0.0%)</option>
				
				<option value="file5">ziaacademy-backend/cmd/server/http/handlers_students.go (33.3%)</option>
				
				<option value="file6">ziaacademy-backend/cmd/server/http/handlers_subjects.go (31.6%)</option>
				
				<option value="file7">ziaacademy-backend/cmd/server/http/handlers_tests.go (38.3%)</option>
				
				<option value="file8">ziaacademy-backend/cmd/server/http/handlers_users.go (0.0%)</option>
				
				<option value="file9">ziaacademy-backend/cmd/server/http/handlers_videos_pdf.go (20.0%)</option>
				
				<option value="file10">ziaacademy-backend/cmd/server/http/http.go (76.2%)</option>
				
				<option value="file11">ziaacademy-backend/cmd/server/http/utils.go (0.0%)</option>
				
				<option value="file12">ziaacademy-backend/db/admins.go (84.6%)</option>
				
				<option value="file13">ziaacademy-backend/db/chapters.go (0.0%)</option>
				
				<option value="file14">ziaacademy-backend/db/content.go (27.3%)</option>
				
				<option value="file15">ziaacademy-backend/db/courses.go (0.0%)</option>
				
				<option value="file16">ziaacademy-backend/db/db.go (50.0%)</option>
				
				<option value="file17">ziaacademy-backend/db/questions.go (0.0%)</option>
				
				<option value="file18">ziaacademy-backend/db/students.go (14.3%)</option>
				
				<option value="file19">ziaacademy-backend/db/subjects.go (25.0%)</option>
				
				<option value="file20">ziaacademy-backend/db/tests.go (39.8%)</option>
				
				<option value="file21">ziaacademy-backend/db/users.go (0.0%)</option>
				
				<option value="file22">ziaacademy-backend/docs/docs.go (100.0%)</option>
				
				<option value="file23">ziaacademy-backend/internal/middleware/middleware.go (14.3%)</option>
				
				<option value="file24">ziaacademy-backend/internal/models/models.go (100.0%)</option>
				
				<option value="file25">ziaacademy-backend/internal/token/token.go (14.6%)</option>
				
				</select>
			</div>
			<div id="legend">
				<span>not tracked</span>
			
				<span class="cov0">not covered</span>
				<span class="cov8">covered</span>
			
			</div>
		</div>
		<div id="content">
		
		<pre class="file" id="file0" style="display: none">package http

import (
        "log/slog"
        "net/http"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "github.com/gin-gonic/gin"
)

// CreateAdmin godoc
//
//        @Summary                Create Admin User
//        @Description        Create a new admin user with role "Admin"
//        @Security       BearerAuth
//        @Param                        admin        body        models.AdminForCreate        true        "Admin user details"
//        @Tags                        admins
//        @Accept                        json
//        @Produce                json
//        @Success                201        {object}        models.User
//        @Failure                400        {object}        HTTPError
//        @Failure                401        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /admins [post]
func (h *Handlers) CreateAdmin(ctx *gin.Context) <span class="cov8" title="1">{
        adminInput := new(models.AdminForCreate)
        if err := ctx.ShouldBindJSON(adminInput); err != nil </span><span class="cov0" title="0">{
                slog.Error("Failed to bind admin input", "error", err.Error())
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">slog.Debug("Processing admin creation request", "email", adminInput.Email)

        // Create admin user model
        admin := models.User{
                FullName:       adminInput.FullName,
                Email:          adminInput.Email,
                PhoneNumber:    adminInput.PhoneNumber,
                ContactAddress: adminInput.ContactAddress,
                Role:           "Admin",
                PasswordHash:   adminInput.Password, // This will be hashed in the database layer
                EmailVerified:  false,
                PhoneVerified:  false,
        }

        createdAdmin, err := h.db.CreateAdmin(ctx.Request.Context(), &amp;admin)
        if err != nil </span><span class="cov0" title="0">{
                slog.Error("Failed to create admin", "email", adminInput.Email,
                        "error", err.Error())
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">slog.Debug("Created admin", "email", createdAdmin.Email)

        // Generate JWT token for the new admin
        token, err := token.GenerateJWT(createdAdmin.ID)
        if err != nil </span><span class="cov0" title="0">{
                slog.Error("Failed to generate token", "email", adminInput.Email, "error", err.Error())
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
                return
        }</span>

        <span class="cov8" title="1">slog.Debug("Generated token", "email", adminInput.Email)

        // Return the token and created admin details
        ctx.JSON(http.StatusCreated, gin.H{
                "token": token,
                "admin": createdAdmin,
        })</span>
}
</pre>
		
		<pre class="file" id="file1" style="display: none">package http

import (
        "net/http"
        "strconv"
        "ziaacademy-backend/internal/models"

        "github.com/gin-gonic/gin"
)

// GetChapters godoc
//
//                        @Summary                Get Chapters
//                        @Description        get chapters for a subject_id
//             @Security       BearerAuth
//                 @Param                        subject_id        query                int        true        "Subject ID"
//                        @Tags                        chapters
//                        @Accept                        json
//                        @Produce                json
//                        @Success                200        {object}        []models.Chapter
//                        @Failure                400        {object}        HTTPError
//                        @Failure                404        {object}        HTTPError
//                        @Failure                500        {object}        HTTPError
//                        @Router                        /chapters [get]
func (h *Handlers) GetChapters(ctx *gin.Context) <span class="cov0" title="0">{
        subStr := ctx.Query("subject_id")
        if subStr == "" </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing subject_id parameter"})
                return
        }</span>
        <span class="cov0" title="0">subjectID, _ := strconv.Atoi(subStr)
        chapters, err := h.db.GetChapters(ctx.Request.Context(), uint(subjectID))
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, chapters)</span>
}

// CreateChapters godoc
//
//                        @Summary                CreateChapters
//                        @Description        create new chapter
//             @Security       BearerAuth
//                 @Param                        item        body        models.ChapterForCreate        true        "chapter details"
//                        @Tags                        chapters
//                        @Accept                        json
//                        @Produce                json
//                        @Success                200        {object}        models.Chapter
//                        @Failure                400        {object}        HTTPError
//                        @Failure                404        {object}        HTTPError
//                        @Failure                500        {object}        HTTPError
//                        @Router                        /chapters [post]
//
// CreateChapter is the HTTP handler to create a new chapter
func (h *Handlers) CreateChapter(ctx *gin.Context) <span class="cov0" title="0">{
        chapterInput := new(models.ChapterForCreate)
        if err := ctx.ShouldBindJSON(chapterInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">chapter := &amp;models.Chapter{
                Name:        chapterInput.Name,
                DisplayName: chapterInput.DisplayName,
        }

        createdChapter, err := h.db.CreateChapter(ctx.Request.Context(), chapter, chapterInput.SubjectName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, createdChapter)</span>
}
</pre>
		
		<pre class="file" id="file2" style="display: none">package http

import (
        "net/http"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "github.com/gin-gonic/gin"
)

// CreateCourse godoc
//
//                        @Summary                CreateCourse
//                        @Description        create new course
//             @Security       BearerAuth
//                 @Param                        item        body        models.CourseForCreate        true        "course details"
//                        @Tags                        courses
//                        @Accept                        json
//                        @Produce                json
//                        @Success                200        {object}        models.Course
//                        @Failure                400        {object}        HTTPError
//                        @Failure                404        {object}        HTTPError
//                        @Failure                500        {object}        HTTPError
//                        @Router                        /courses [post]
func (h *Handlers) CreateCourse(ctx *gin.Context) <span class="cov0" title="0">{
        courseInput := new(models.CourseForCreate)
        if err := ctx.ShouldBindJSON(courseInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">course := &amp;models.Course{
                Name:           courseInput.Name,
                Description:    courseInput.Description,
                Price:          courseInput.Price,
                Discount:       courseInput.Discount,
                DurationInDays: courseInput.DurationInDays,
        }

        createdCourse, err := h.db.CreateCourse(ctx.Request.Context(), course)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, createdCourse)</span>
}

// GetCourses godoc
//
//                @Summary                Get Courses
//                @Description        get courses for the logged in student
//         @Security       BearerAuth
//                @Tags                        courses
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        []models.CourseWithPurchased
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /courses [get]
//
// GetCourses is the HTTP handler to get list of courses
// This handler does not use any framework, instead just the standard library
func (h *Handlers) GetCourses(ctx *gin.Context) <span class="cov0" title="0">{
        userID, err := token.ExtractTokenID(ctx)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">courses, err := h.db.GetCourses(ctx.Request.Context(),
                userID)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, gin.H{"courses": courses})</span>
}
</pre>
		
		<pre class="file" id="file3" style="display: none">package http

import (
        "net/http"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "github.com/gin-gonic/gin"
)

// Login godoc
//
//                @Summary                Login
//                @Description        login with email and password
//         @Param                        item        body        models.Credentials        true        "user_email and password"
//                @Tags                        login
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        string
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /login [post]
//
// Login is the HTTP handler to login to the application
func (h *Handlers) Login(ctx *gin.Context) <span class="cov0" title="0">{
        creds := new(models.Credentials)
        if err := ctx.ShouldBindJSON(creds); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        // Check if the username exists and password matches
        <span class="cov0" title="0">userID, err := h.db.ValidateUserPassword(ctx, creds.UserEmail, creds.Password)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
                return
        }</span>

        // Generate JWT token
        <span class="cov0" title="0">token, err := token.GenerateJWT(userID)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
                return
        }</span>

        <span class="cov0" title="0">student, err := h.db.GetStudentByUserID(ctx, userID)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve student details"})
                return
        }</span>
        // Return the token to the user
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, gin.H{"token": token, "student": student})</span>
}
</pre>
		
		<pre class="file" id="file4" style="display: none">package http

import (
        "net/http"
        "ziaacademy-backend/internal/models"

        "github.com/gin-gonic/gin"
)

// CreateTopic godoc
//
//                        @Summary                CreateTopic
//                        @Description        create new topic for questions
//             @Security       BearerAuth
//                 @Param                        item        body        models.TopicForCreate        true        "topic details"
//                        @Tags                        questions
//                        @Accept                        json
//                        @Produce                json
//                        @Success                200        {object}        models.Topic
//                        @Failure                400        {object}        HTTPError
//                        @Failure                404        {object}        HTTPError
//                        @Failure                500        {object}        HTTPError
//                        @Router                        /topics [post]
func (h *Handlers) CreateTopic(ctx *gin.Context) <span class="cov0" title="0">{
        topicInput := new(models.TopicForCreate)
        if err := ctx.ShouldBindJSON(topicInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">topic := &amp;models.Topic{
                Name: topicInput.Name,
        }

        createdTopic, err := h.db.CreateTopic(ctx.Request.Context(), topic, topicInput.ChapterName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, createdTopic)</span>
}

// CreateQuestion godoc
//
//                        @Summary                CreateQuestion
//                        @Description        create new question
//             @Security       BearerAuth
//                 @Param                        item        body        models.QuestionForCreate        true        "question details"
//                        @Tags                        questions
//                        @Accept                        json
//                        @Produce                json
//                        @Success                200        {object}        models.Question
//                        @Failure                400        {object}        HTTPError
//                        @Failure                404        {object}        HTTPError
//                        @Failure                500        {object}        HTTPError
//                        @Router                        /questions [post]
func (h *Handlers) CreateQuestion(ctx *gin.Context) <span class="cov0" title="0">{
        questionInput := new(models.QuestionForCreate)
        if err := ctx.ShouldBindJSON(questionInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">question := &amp;models.Question{
                Text:          questionInput.Text,
                QuestionType:  questionInput.QuestionType,
                ImageUrl:      questionInput.ImageUrl,
                FileUrl:       questionInput.FileUrl,
                CorrectAnswer: questionInput.CorrectAnswer,
        }

        createdQuestion, err := h.db.CreateQuestion(ctx.Request.Context(), question, questionInput.TopicName, questionInput.DifficultyName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, createdQuestion)</span>
}

// GetQuestions godoc
//
//        @Summary                Get Questions
//        @Description        get questions for given topic and difficulty
//             @Security       BearerAuth
//                 @Param                        topic        query                string        true        "Topic name"
//
// @Param                        difficulty        query                string        true        "Difficulty ()"
//
//        @Tags                        questions
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        []models.Question
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /questions [get]
//
// GetQuestions is the HTTP handler to get list of questions for
// given topic and difficulty level
func (h *Handlers) GetQuestions(ctx *gin.Context) <span class="cov0" title="0">{
        topicStr := ctx.Query("topic")
        if topicStr == "" </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing topic parameter"})
                return
        }</span>
        <span class="cov0" title="0">difficulty := ctx.Query("difficulty")
        if difficulty == "" </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing difficulty parameter"})
                return
        }</span>
        <span class="cov0" title="0">content, err := h.db.GetQuestions(ctx.Request.Context(), topicStr,
                difficulty)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, gin.H{"content": content})</span>
}
</pre>
		
		<pre class="file" id="file5" style="display: none">package http

import (
        "log/slog"
        "net/http"
        "strconv"

        "github.com/gin-gonic/gin"

        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"
)

// CreateStudent godoc
//
//                @Summary                CreateStudent
//                @Description        create new student
//         @Param                        item        body        models.StudentForCreate        true        "student details"
//                @Tags                        students
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        models.CreatedStudentResponse
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /students [post]
func (h *Handlers) CreateStudent(ctx *gin.Context) <span class="cov8" title="1">{
        stuInput := new(models.StudentForCreate)
        if err := ctx.ShouldBindJSON(stuInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov8" title="1">slog.Debug("Processing student creation request", "email", stuInput.Email)
        // var stu models.Student
        stu := models.Student{
                User: models.User{
                        FullName:       stuInput.FullName,
                        Email:          stuInput.Email,
                        PhoneNumber:    stuInput.PhoneNumber,
                        ContactAddress: stuInput.ContactAddress,
                        Role:           "Student",
                },
                ParentPhone: stuInput.ParentPhone,
                ParentEmail: stuInput.ParentEmail,
        }

        createdStudent, err := h.db.CreateStudent(ctx.Request.Context(), &amp;stu)
        if err != nil </span><span class="cov0" title="0">{
                slog.Error("Failed to create student", "email", stuInput.Email,
                        "error", err.Error())
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">slog.Debug("Created student", "email", createdStudent.User.Email)
        // Generate JWT token
        token, err := token.GenerateJWT(createdStudent.UserID)
        if err != nil </span><span class="cov0" title="0">{
                slog.Error("Failed to generate token", "email", stuInput.Email, "error", err.Error())
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
                return
        }</span>

        <span class="cov8" title="1">slog.Debug("Generated token", "email", stuInput.Email)
        // Return the token to the user
        ctx.JSON(http.StatusOK, gin.H{"token": token, "createdStudent": createdStudent})</span>
}

// EnrollInCourse godoc
//
//                @Summary                EnrollInCourse
//                @Description        enroll student in a course
//             @Security       BearerAuth
//         @Param                        course_id        path        uint        true        "course ID to enroll in"
//                @Tags                        students
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        models.Student
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /enroll/{course_id} [post]
func (h *Handlers) EnrollInCourse(ctx *gin.Context) <span class="cov0" title="0">{
        userID, err := token.ExtractTokenID(ctx)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">courseIDStr, ok := ctx.Params.Get("course_id")
        if !ok </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing course_id parameter"})
                return
        }</span>
        <span class="cov0" title="0">courseID, _ := strconv.Atoi(courseIDStr)
        updatedStudent, err := h.db.EnrollStudentInCourse(ctx.Request.Context(),
                userID, uint(courseID))

        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        // Return the token to the user
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, updatedStudent)</span>
}
</pre>
		
		<pre class="file" id="file6" style="display: none">package http

import (
        "net/http"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "github.com/gin-gonic/gin"
)

// GetSubjects godoc
//
//        @Summary                Get Subjects
//        @Description        get subjects for logged in student
//             @Security       BearerAuth
//        @Tags                        library
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        []models.Subject
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /subjects [get]
//
// GetQuestions is the HTTP handler to get list of questions for
// given topic and difficulty level
// GetSubjects is the HTTP handler to get subjects for a student
// This handler does not use any framework, instead just the standard library
func (h *Handlers) GetSubjects(ctx *gin.Context) <span class="cov0" title="0">{
        userID, err := token.ExtractTokenID(ctx)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">subjects, err := h.db.GetSubjects(ctx.Request.Context(), userID)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, gin.H{"subjects": subjects})</span>
}

// CreateSubject godoc
//
//                @Summary                CreateSubject
//                @Description        create new subject
//             @Security       BearerAuth
//         @Param                        item        body        models.SubjectForCreate        true        "subject details"
//                @Tags                        subjects
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        models.Subject
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /subjects [post]
func (h *Handlers) CreateSubject(ctx *gin.Context) <span class="cov8" title="1">{
        subjectInput := new(models.SubjectForCreate)
        if err := ctx.ShouldBindJSON(subjectInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">subject := &amp;models.Subject{
                Name:        subjectInput.Name,
                DisplayName: subjectInput.DisplayName,
        }

        createdSubject, err := h.db.CreateSubject(ctx.Request.Context(), subject)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov8" title="1">ctx.JSON(http.StatusOK, createdSubject)</span>
}
</pre>
		
		<pre class="file" id="file7" style="display: none">package http

import (
        "net/http"
        "strconv"
        "ziaacademy-backend/internal/models"

        "github.com/gin-gonic/gin"
)

// CreateSectionType godoc
//
//        @Summary                CreateSectionType
//        @Description        create new section type
//        @Security       BearerAuth
//        @Param                        item        body        models.SectionTypeForCreate        true        "section type details"
//        @Tags                        tests
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        models.SectionType
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /section-types [post]
func (h *Handlers) CreateSectionType(ctx *gin.Context) <span class="cov8" title="1">{
        sectionTypeInput := new(models.SectionTypeForCreate)
        if err := ctx.ShouldBindJSON(sectionTypeInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">sectionType := &amp;models.SectionType{
                Name:          sectionTypeInput.Name,
                QuestionCount: sectionTypeInput.QuestionCount,
                PositiveMarks: sectionTypeInput.PositiveMarks,
                NegativeMarks: sectionTypeInput.NegativeMarks,
        }

        createdSectionType, err := h.db.CreateSectionType(ctx.Request.Context(), sectionType, sectionTypeInput.SubjectName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">ctx.JSON(http.StatusOK, createdSectionType)</span>
}

// CreateTestType godoc
//
//        @Summary                CreateTestType
//        @Description        create new test type
//        @Security       BearerAuth
//        @Param                        item        body        models.TestTypeForCreate        true        "test type details"
//        @Tags                        tests
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        models.TestType
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /test-types [post]
func (h *Handlers) CreateTestType(ctx *gin.Context) <span class="cov8" title="1">{
        testTypeInput := new(models.TestTypeForCreate)
        if err := ctx.ShouldBindJSON(testTypeInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">testType := &amp;models.TestType{
                Name: testTypeInput.Name,
        }

        createdTestType, err := h.db.CreateTestType(ctx.Request.Context(), testType, testTypeInput.SectionTypeNames)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">ctx.JSON(http.StatusOK, createdTestType)</span>
}

// CreateTest godoc
//
//        @Summary                CreateTest
//        @Description        create new test of a given type
//        @Security       BearerAuth
//        @Param                        item        body        models.TestForCreate        true        "test details"
//        @Tags                        tests
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        models.Test
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /tests [post]
func (h *Handlers) CreateTest(ctx *gin.Context) <span class="cov8" title="1">{
        testInput := new(models.TestForCreate)
        if err := ctx.ShouldBindJSON(testInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">test := &amp;models.Test{
                Name:        testInput.Name,
                FromTime:    testInput.FromTime,
                ToTime:      testInput.ToTime,
                Active:      true, // Default to active
                Description: testInput.Description,
        }

        createdTest, err := h.db.CreateTest(ctx.Request.Context(), test, testInput.Sections, testInput.TestTypeName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">ctx.JSON(http.StatusOK, createdTest)</span>
}

// AddQuestionsToTest godoc
//
//        @Summary                AddQuestionsToTest
//        @Description        add questions to a test
//        @Security       BearerAuth
//        @Param                        test_id        path        int        true        "Test ID"
//        @Param                        item        body        []uint        true        "question IDs"
//        @Tags                        tests
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        map[string]string
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /tests/{test_id}/questions [post]
func (h *Handlers) AddQuestionsToTest(ctx *gin.Context) <span class="cov0" title="0">{
        testIDStr := ctx.Param("test_id")
        testID, err := strconv.ParseUint(testIDStr, 10, 32)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid test ID"})
                return
        }</span>

        <span class="cov0" title="0">var questionIDs []uint
        if err := ctx.ShouldBindJSON(&amp;questionIDs); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">if len(questionIDs) == 0 </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "No question IDs provided"})
                return
        }</span>

        <span class="cov0" title="0">err = h.db.AddQuestionsToTest(ctx.Request.Context(), uint(testID), questionIDs)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">ctx.JSON(http.StatusOK, gin.H{"message": "Questions added to test successfully"})</span>
}
</pre>
		
		<pre class="file" id="file8" style="display: none">package http

import (
        "net/http"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "github.com/gin-gonic/gin"
)

// UpdatePassword godoc
//
//                @Summary                UpdatePassword
//                @Description        update password for logged in user
//             @Security       BearerAuth
//         @Param                        item        body        models.UpdatePassword        true        "new password"
//                @Tags                        users
//                @Accept                        json
//                @Produce                json
//                @Success                200
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /users/password [post]
func (h *Handlers) UpdatePassword(ctx *gin.Context) <span class="cov0" title="0">{
        userID, err := token.ExtractTokenID(ctx)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">update := new(models.UpdatePassword)
        if err := ctx.ShouldBindJSON(update); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">err = h.db.UpdatePassword(ctx.Request.Context(), userID,
                update.NewPassword)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, struct{}{})</span>
}
</pre>
		
		<pre class="file" id="file9" style="display: none">package http

import (
        "net/http"
        "strconv"
        "ziaacademy-backend/internal/models"

        "github.com/gin-gonic/gin"
)

// GetContent godoc
//
//        @Summary                Get Content
//        @Description        get videos and studymaterial for given chapter
//             @Security       BearerAuth
//
// @Param                        chapter_id        query                uint        true        "Chapter ID"
//
//        @Tags                        questions
//        @Accept                        json
//        @Produce                json
//        @Success                200        {object}        models.Content
//        @Failure                400        {object}        HTTPError
//        @Failure                404        {object}        HTTPError
//        @Failure                500        {object}        HTTPError
//        @Router                        /content [get]
//
// GetQuestions is the HTTP handler to get list of questions for
// given topic and difficulty level
// GetContent is the HTTP handler to get content for a chapter
// This handler does not use any framework, instead just the standard library
func (h *Handlers) GetContent(ctx *gin.Context) <span class="cov0" title="0">{
        chStr := ctx.Query("chapter_id")
        if chStr == "" </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing chapter_id parameter"})
                return
        }</span>
        <span class="cov0" title="0">chapterID, _ := strconv.Atoi(chStr)
        content, err := h.db.GetContent(ctx.Request.Context(), uint(chapterID))
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, gin.H{"content": content})</span>
}

// AddVideo godoc
//
//                @Summary            AddVideo
//                @Description        add a new video
//             @Security       BearerAuth
//         @Param                        item        body        models.VideoForCreate        true        "video details"
//                @Tags                        library
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        models.Video
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /videos [post]
func (h *Handlers) AddVideo(ctx *gin.Context) <span class="cov8" title="1">{
        videoInput := new(models.VideoForCreate)
        if err := ctx.ShouldBindJSON(videoInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov8" title="1">video := &amp;models.Video{
                Name:        videoInput.Name,
                DisplayName: videoInput.DisplayName,
                VideoUrl:    videoInput.VideoUrl,
                ViewCount:   0, // Default to 0
        }

        createdVideo, err := h.db.AddVideo(ctx.Request.Context(), video, videoInput.ChapterName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov8" title="1">ctx.JSON(http.StatusOK, createdVideo)</span>
}

// AddStudyMaterial godoc
//
//                @Summary            AddStudyMaterial
//                @Description        add a new material
//             @Security       BearerAuth
//         @Param                        item        body        models.MaterialForCreate        true        "material details"
//                @Tags                        library
//                @Accept                        json
//                @Produce                json
//                @Success                200        {object}        models.StudyMaterial
//                @Failure                400        {object}        HTTPError
//                @Failure                404        {object}        HTTPError
//                @Failure                500        {object}        HTTPError
//                @Router                        /studymaterials [post]
//
// AddStudyMaterial is the HTTP handler to add a new pdf to a chapter
func (h *Handlers) AddStudyMaterial(ctx *gin.Context) <span class="cov0" title="0">{
        materialInput := new(models.MaterialForCreate)
        if err := ctx.ShouldBindJSON(materialInput); err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
                return
        }</span>

        <span class="cov0" title="0">material := &amp;models.StudyMaterial{
                Name:        materialInput.Name,
                DisplayName: materialInput.DisplayName,
                Url:         materialInput.Url,
        }

        createdMaterial, err := h.db.AddStudyMaterial(ctx.Request.Context(), material, materialInput.ChapterName)
        if err != nil </span><span class="cov0" title="0">{
                ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
                return
        }</span>
        <span class="cov0" title="0">ctx.JSON(http.StatusOK, createdMaterial)</span>
}
</pre>
		
		<pre class="file" id="file10" style="display: none">package http

import (
        "fmt"
        "io"
        "os"
        "time"

        "ziaacademy-backend/db"
        "ziaacademy-backend/docs"
        "ziaacademy-backend/internal/middleware"

        "github.com/gin-gonic/gin"
        "github.com/naughtygopher/errors"
        swaggerFiles "github.com/swaggo/files"
        ginSwagger "github.com/swaggo/gin-swagger"
)

// Config holds all the configuration required to start the HTTP server
type Config struct {
        Host string
        Port uint16

        ReadTimeout  time.Duration
        WriteTimeout time.Duration
        DialTimeout  time.Duration

        TemplatesBasePath string
        EnableAccessLog   bool
}

// Handlers struct has all the dependencies required for HTTP handlers
type Handlers struct {
        db db.Server
}

func NewHandlers(db db.Server) *Handlers <span class="cov8" title="1">{
        return &amp;Handlers{
                db: db,
        }
}</span>

//  @title                        ZIA Academy App
//        @version                1.0
//        @description        Backend server for ZIA Academy.
//        @termsOfService        http://swagger.io/terms/

//        @contact.name        API Support
//        @contact.url        http://www.swagger.io/support
//        @contact.email        <EMAIL>

//        @license.name        Apache 2.0
//        @license.url        http://www.apache.org/licenses/LICENSE-2.0.html

//        @host                localhost:443
//        @BasePath        /api/

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
func SetupRouter(svr db.Server, logFile *os.File, skipAuth bool) *gin.Engine <span class="cov8" title="1">{
        gin.DefaultWriter = io.MultiWriter(logFile)
        router := gin.Default()

        h := NewHandlers(svr)

        open := router.Group("api")
        open.POST("/students", h.CreateStudent)
        open.POST("/login", h.Login)

        protected := router.Group("api")
        if !skipAuth </span><span class="cov8" title="1">{
                protected.Use(middleware.JwtAuthMiddleware())
        }</span>

        <span class="cov8" title="1">docs.SwaggerInfo.BasePath = "/api"

        protected.POST("/courses", h.CreateCourse)
        protected.POST("/subjects", h.CreateSubject)
        protected.POST("/chapters", h.CreateChapter)
        protected.POST("/topics", h.CreateTopic)
        protected.POST("/questions", h.CreateQuestion)
        protected.POST("/videos", h.AddVideo)
        protected.POST("/studymaterials", h.AddStudyMaterial)
        protected.GET("/subjects", h.GetSubjects)
        protected.GET("/chapters", h.GetChapters)
        protected.GET("/courses", h.GetCourses)
        protected.GET("/content", h.GetContent)
        protected.POST("/users/password", h.UpdatePassword)
        protected.GET("/questions", h.GetQuestions)
        protected.POST("/enroll/:course_id", h.EnrollInCourse)
        protected.POST("/admins", h.CreateAdmin)

        // Test-related routes
        protected.POST("/section-types", h.CreateSectionType)
        protected.POST("/test-types", h.CreateTestType)
        protected.POST("/tests", h.CreateTest)
        protected.POST("/tests/:test_id/questions", h.AddQuestionsToTest)

        // use ginSwagger middleware to serve the API docs
        router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
        return router</span>
}

func StartServer(svr db.Server, httpCfg *Config,
        logFile *os.File,
        sslCert, sslKey string,
        fatalErr chan&lt;- error) *gin.Engine <span class="cov0" title="0">{

        router := SetupRouter(svr, logFile, false)
        go func() </span><span class="cov0" title="0">{
                defer func() </span><span class="cov0" title="0">{
                        rec := recover()
                        if rec != nil </span><span class="cov0" title="0">{
                                fatalErr &lt;- errors.New(fmt.Sprintf("%+v", rec))
                        }</span>
                }()
                <span class="cov0" title="0">err := router.RunTLS(fmt.Sprintf("%s:%d",
                        httpCfg.Host, httpCfg.Port), sslCert, sslKey)
                if err != nil </span><span class="cov0" title="0">{
                        fatalErr &lt;- errors.Wrap(err, "failed to start HTTP server")
                }</span>
        }()

        <span class="cov0" title="0">return router</span>
}
</pre>
		
		<pre class="file" id="file11" style="display: none">package http

import "github.com/gin-gonic/gin"

// NewError example
func NewError(ctx *gin.Context, status int, err error) <span class="cov0" title="0">{
        er := HTTPError{
                Code:    status,
                Message: err.Error(),
        }
        ctx.JSON(status, er)
}</span>

// HTTPError example
type HTTPError struct {
        Code    int    `json:"code" example:"400"`
        Message string `json:"message" example:"status bad request"`
}
</pre>
		
		<pre class="file" id="file12" style="display: none">package db

import (
        "context"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "golang.org/x/crypto/bcrypt"
)

func (p *DbPlugin) CreateAdmin(ctx context.Context, admin *models.User) (*models.User, error) <span class="cov8" title="1">{
        // Hash the password before storing
        if admin.PasswordHash != "" </span><span class="cov8" title="1">{
                combined := admin.PasswordHash + token.SecretKeyStr
                hash, err := bcrypt.GenerateFromPassword([]byte(combined), bcrypt.DefaultCost)
                if err != nil </span><span class="cov0" title="0">{
                        return nil, err
                }</span>
                <span class="cov8" title="1">admin.PasswordHash = string(hash)</span>
        }

        // Ensure the role is set to Admin
        <span class="cov8" title="1">admin.Role = "Admin"

        // Sanitize the user data
        admin.Sanitize()

        // Create the admin user in the database
        res := p.db.Create(admin)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // Clear the password hash from the returned object for security
        <span class="cov8" title="1">admin.PasswordHash = ""

        return admin, nil</span>
}
</pre>
		
		<pre class="file" id="file13" style="display: none">package db

import (
        "context"
        "fmt"
        "log"
        "ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetChapters(ctx context.Context, subject_id uint) ([]models.Chapter, error) <span class="cov0" title="0">{
        var subject models.Subject

        // Retrieve the subject with given ID, along with its chapters
        if err := p.db.Preload("Chapters").First(&amp;subject, subject_id).Error; err != nil </span><span class="cov0" title="0">{
                // Handle error if retrieval fails
                log.Println("Error retrieving subject:", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">return subject.Chapters, nil</span>
}

func (p *DbPlugin) CreateChapter(ctx context.Context, chapter *models.Chapter, subjectName string) (*models.Chapter, error) <span class="cov0" title="0">{
        // Find the subject by name
        var subject models.Subject
        if err := p.db.Where("name = ?", subjectName).First(&amp;subject).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("subject '%s' not found: %w", subjectName, err)
        }</span>

        // Set the subject ID
        <span class="cov0" title="0">chapter.SubjectID = subject.ID

        res := p.db.Create(chapter)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // Load the chapter with subject association
        <span class="cov0" title="0">if err := p.db.Preload("Subject").First(chapter, chapter.ID).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to load chapter with subject: %w", err)
        }</span>

        <span class="cov0" title="0">return chapter, nil</span>
}
</pre>
		
		<pre class="file" id="file14" style="display: none">package db

import (
        "context"
        "fmt"
        "log"
        "ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetContent(ctx context.Context, chapter_id uint) (*models.Content, error) <span class="cov0" title="0">{
        var chapter models.Chapter
        // Retrieve the subject with given ID, along with its chapters
        if err := p.db.Preload("Videos").Preload("StudyMaterials").
                First(&amp;chapter, chapter_id).Error; err != nil </span><span class="cov0" title="0">{
                // Handle error if retrieval fails
                log.Println("Error retrieving chapter:", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">content := &amp;models.Content{
                Videos: chapter.Videos,
                Pdfs:   chapter.StudyMaterials,
        }
        return content, nil</span>
}

func (p *DbPlugin) AddVideo(ctx context.Context, video *models.Video, chapterName string) (*models.Video, error) <span class="cov8" title="1">{
        // Find the chapter by name
        var chapter models.Chapter
        if err := p.db.Where("name = ?", chapterName).First(&amp;chapter).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
        }</span>

        // Set the chapter ID
        <span class="cov8" title="1">video.ChapterID = chapter.ID

        res := p.db.Create(video)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // No need to preload Chapter since Video doesn't have Chapter relationship
        // The video is already created with the correct ChapterID

        <span class="cov8" title="1">return video, nil</span>
}

func (p *DbPlugin) AddStudyMaterial(ctx context.Context,
        pdf *models.StudyMaterial, chapterName string) (*models.StudyMaterial, error) <span class="cov0" title="0">{
        // Find the chapter by name
        var chapter models.Chapter
        if err := p.db.Where("name = ?", chapterName).First(&amp;chapter).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
        }</span>

        // Set the chapter ID
        <span class="cov0" title="0">pdf.ChapterID = chapter.ID

        res := p.db.Create(pdf)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // No need to preload Chapter since StudyMaterial doesn't have Chapter relationship
        // The study material is already created with the correct ChapterID

        <span class="cov0" title="0">return pdf, nil</span>
}
</pre>
		
		<pre class="file" id="file15" style="display: none">package db

import (
        "context"
        "ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateCourse(ctx context.Context, course *models.Course) (*models.Course, error) <span class="cov0" title="0">{
        res := p.db.Create(course)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>
        <span class="cov0" title="0">return course, nil</span>
}

func (p *DbPlugin) GetCourses(ctx context.Context,
        userID uint) ([]models.CourseWithPurchased, error) <span class="cov0" title="0">{
        var student models.Student
        var allCourses []models.Course
        var coursesWithPurchased []models.CourseWithPurchased

        // Step 1: Find student and load associated courses
        p.db.Preload("User").Preload("Courses").
                Where("user_id = ?", userID).
                First(&amp;student)

        // Step 2: Query for all courses
        p.db.Find(&amp;allCourses)

        // Step 3: For each course, check if the student enrolled for it
        // and update the data to be returned accordingly
        for _, c := range allCourses </span><span class="cov0" title="0">{
                var purchased bool
                for _, sc := range student.Courses </span><span class="cov0" title="0">{
                        if c.ID == sc.ID </span><span class="cov0" title="0">{
                                purchased = true
                                break</span>
                        }
                }
                <span class="cov0" title="0">toAdd := models.CourseWithPurchased{
                        Name:           c.Name,
                        Description:    c.Description,
                        Price:          c.Price,
                        Discount:       c.Discount,
                        DurationInDays: c.DurationInDays,
                        Purchased:      purchased,
                }
                coursesWithPurchased = append(coursesWithPurchased,
                        toAdd)</span>
        }
        <span class="cov0" title="0">return coursesWithPurchased, nil</span>
}
</pre>
		
		<pre class="file" id="file16" style="display: none">package db

import (
        "context"
        "ziaacademy-backend/internal/models"

        "gorm.io/gorm"
)

type DbPlugin struct {
        db *gorm.DB
}

// Server has all the methods required to run the server
type Server interface {
        CreateStudent(ctx context.Context, user *models.Student) (*models.Student, error)
        CreateAdmin(ctx context.Context, admin *models.User) (*models.User, error)
        CreateCourse(ctx context.Context, course *models.Course) (*models.Course, error)
        CreateSubject(ctx context.Context, subject *models.Subject) (*models.Subject, error)
        GetSubjects(ctx context.Context, student_id uint) ([]models.Subject, error)
        GetChapters(ctx context.Context, subject_id uint) ([]models.Chapter, error)
        GetContent(ctx context.Context, chapter_id uint) (*models.Content, error)
        GetCourses(ctx context.Context,
                userID uint) ([]models.CourseWithPurchased, error)
        ValidateUserPassword(ctx context.Context, userEmail, pwd string) (uint, error)
        UpdatePassword(ctx context.Context, userID uint, newPwd string) error
        AddVideo(ctx context.Context, video *models.Video, chapterName string) (*models.Video, error)
        AddStudyMaterial(ctx context.Context,
                pdf *models.StudyMaterial, chapterName string) (*models.StudyMaterial, error)
        CreateChapter(ctx context.Context, chapter *models.Chapter, subjectName string) (*models.Chapter, error)
        CreateTopic(ctx context.Context, topic *models.Topic, chapterName string) (*models.Topic, error)
        CreateQuestion(ctx context.Context, question *models.Question, topicName, difficultyName string) (*models.Question, error)
        GetQuestions(ctx context.Context, topicName,
                difficulty string) ([]models.Question, error)
        EnrollStudentInCourse(ctx context.Context, userID,
                courseID uint) (*models.Student, error)
        GetStudentByUserID(ctx context.Context,
                userID uint) (*models.StudentForCreate, error)
        CreateSectionType(ctx context.Context, sectionType *models.SectionType, subjectName string) (*models.SectionType, error)
        CreateTestType(ctx context.Context, testType *models.TestType, sectionTypeNames []string) (*models.TestType, error)
        CreateTest(ctx context.Context, test *models.Test, sections []models.SectionForCreate, testTypeName string) (*models.Test, error)
        AddQuestionsToTest(ctx context.Context, testID uint, questionIDs []uint) error
}

func NewServer(db *gorm.DB) Server <span class="cov8" title="1">{
        return &amp;DbPlugin{
                db: db,
        }
}</span>
func NewDbPlugin(db *gorm.DB) *DbPlugin <span class="cov0" title="0">{
        return &amp;DbPlugin{
                db: db,
        }
}</span>
</pre>
		
		<pre class="file" id="file17" style="display: none">package db

import (
        "context"
        "fmt"
        "ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateTopic(ctx context.Context, topic *models.Topic, chapterName string) (*models.Topic, error) <span class="cov0" title="0">{
        // Find the chapter by name
        var chapter models.Chapter
        if err := p.db.Where("name = ?", chapterName).First(&amp;chapter).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("chapter '%s' not found: %w", chapterName, err)
        }</span>

        // Set the chapter ID
        <span class="cov0" title="0">topic.ChapterID = chapter.ID

        res := p.db.Create(topic)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // Load the topic with chapter association
        <span class="cov0" title="0">if err := p.db.Preload("Chapter").First(topic, topic.ID).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to load topic with chapter: %w", err)
        }</span>

        <span class="cov0" title="0">return topic, nil</span>
}

func (p *DbPlugin) CreateQuestion(ctx context.Context, question *models.Question, topicName, difficultyName string) (*models.Question, error) <span class="cov0" title="0">{
        // Find the topic by name
        var topic models.Topic
        if err := p.db.Where("name = ?", topicName).First(&amp;topic).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("topic '%s' not found: %w", topicName, err)
        }</span>

        // Find the difficulty by name
        <span class="cov0" title="0">var difficulty models.Difficulty
        if err := p.db.Where("name = ?", difficultyName).First(&amp;difficulty).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("difficulty '%s' not found: %w", difficultyName, err)
        }</span>

        // Set the IDs
        <span class="cov0" title="0">question.TopicID = topic.ID
        question.DifficultyID = difficulty.ID

        res := p.db.Create(question)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // Load the question with topic and difficulty associations
        <span class="cov0" title="0">if err := p.db.Preload("Topic").Preload("Difficulty").First(question, question.ID).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to load question with associations: %w", err)
        }</span>

        <span class="cov0" title="0">return question, nil</span>
}

func (p *DbPlugin) GetQuestions(ctx context.Context, topicName,
        difficulty string) ([]models.Question, error) <span class="cov0" title="0">{
        var questions []models.Question

        err := p.db.Preload("Topic").
                Preload("Difficulty").
                Joins("JOIN topics ON topics.id = questions.topic_id").
                Joins("JOIN difficulties ON difficulties.id = questions.difficulty_id").
                Where("topics.name = ? AND difficulties.name = ?", topicName, difficulty).
                Find(&amp;questions).Error

        if err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to get questions: %w", err)
        }</span>
        <span class="cov0" title="0">return questions, nil</span>
}
</pre>
		
		<pre class="file" id="file18" style="display: none">package db

import (
        "context"
        "fmt"
        "ziaacademy-backend/internal/models"
)

func (p *DbPlugin) CreateStudent(ctx context.Context, student *models.Student) (*models.Student, error) <span class="cov8" title="1">{
        res := p.db.Create(student)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>
        <span class="cov8" title="1">return student, nil</span>
}

func (p *DbPlugin) EnrollStudentInCourse(ctx context.Context, userID,
        courseID uint) (*models.Student, error) <span class="cov0" title="0">{
        var student models.Student
        var course models.Course
        if err := p.db.Preload("User").Preload("Courses").
                Where("user_id = ?", userID).First(&amp;student).Error; err != nil </span><span class="cov0" title="0">{
                // Handle error
                fmt.Println("Error retrieving student:", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">if err := p.db.
                Where("id = ?", courseID).First(&amp;course).Error; err != nil </span><span class="cov0" title="0">{
                // Handle error
                fmt.Println("Error retrieving course:", err)
                return nil, err
        }</span>

        <span class="cov0" title="0">if err := p.db.Model(&amp;student).Association("Courses").Append(&amp;course); err != nil </span><span class="cov0" title="0">{
                return nil, err
        }</span>
        <span class="cov0" title="0">return &amp;student, nil</span>
}

func (p *DbPlugin) GetStudentByUserID(ctx context.Context,
        userID uint) (*models.StudentForCreate, error) <span class="cov0" title="0">{
        var student models.Student
        if err := p.db.Preload("User").
                Where("user_id = ?", userID).First(&amp;student).Error; err != nil </span><span class="cov0" title="0">{
                // Handle error
                fmt.Println("Error retrieving student:", err)
                return nil, err
        }</span>
        <span class="cov0" title="0">studentToReturn := &amp;models.StudentForCreate{
                UserForCreate: models.UserForCreate{
                        FullName:       student.User.FullName,
                        Email:          student.User.Email,
                        PhoneNumber:    student.User.PhoneNumber,
                        ContactAddress: student.User.ContactAddress,
                },
                ParentPhone: student.ParentPhone,
                ParentEmail: student.ParentEmail,
        }
        return studentToReturn, nil</span>
}
</pre>
		
		<pre class="file" id="file19" style="display: none">package db

import (
        "context"
        "fmt"
        "ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetSubjects(ctx context.Context, userID uint) ([]models.Subject, error) <span class="cov0" title="0">{
        var student models.Student
        var subjects []models.Subject

        // Find the student with ID student_id
        if err := p.db.Preload("Courses.Subjects").
                Where("user_id = ?", userID).First(&amp;student).Error; err != nil </span><span class="cov0" title="0">{
                // Handle error
                fmt.Println("Error retrieving student:", err)
                return nil, err
        }</span>

        // Loop through the courses and collect subjects
        <span class="cov0" title="0">for _, course := range student.Courses </span><span class="cov0" title="0">{
                subjects = append(subjects, course.Subjects...)
        }</span>

        <span class="cov0" title="0">return subjects, nil</span>
}

func (p *DbPlugin) CreateSubject(ctx context.Context, subject *models.Subject) (*models.Subject, error) <span class="cov8" title="1">{
        res := p.db.Save(subject)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>
        <span class="cov8" title="1">return subject, nil</span>
}
</pre>
		
		<pre class="file" id="file20" style="display: none">package db

import (
        "context"
        "errors"
        "fmt"
        "ziaacademy-backend/internal/models"

        "gorm.io/gorm"
)

func (p *DbPlugin) CreateSectionType(ctx context.Context, sectionType *models.SectionType, subjectName string) (*models.SectionType, error) <span class="cov8" title="1">{
        // Find the subject by name
        var subject models.Subject
        if err := p.db.Where("name = ?", subjectName).First(&amp;subject).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("subject '%s' not found: %w", subjectName, err)
        }</span>

        // Set the subject ID
        <span class="cov8" title="1">sectionType.SubjectID = subject.ID

        res := p.db.Create(sectionType)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // Load the section type with subject association
        <span class="cov8" title="1">if err := p.db.Preload("Subject").First(sectionType, sectionType.ID).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to load section type with subject: %w", err)
        }</span>

        <span class="cov8" title="1">return sectionType, nil</span>
}

func (p *DbPlugin) CreateTestType(ctx context.Context, testType *models.TestType, sectionTypeNames []string) (*models.TestType, error) <span class="cov8" title="1">{
        // First, find the section types by their names
        var sectionTypes []models.SectionType
        if len(sectionTypeNames) &gt; 0 </span><span class="cov8" title="1">{
                // Find existing section types
                if err := p.db.Where("name IN ?", sectionTypeNames).Find(&amp;sectionTypes).Error; err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to find section types: %w", err)
                }</span>

                // Check if all section types were found
                <span class="cov8" title="1">if len(sectionTypes) != len(sectionTypeNames) </span><span class="cov0" title="0">{
                        return nil, errors.New("one or more section types not found")
                }</span>
        }

        // Create the test type
        <span class="cov8" title="1">testTypeToCreate := &amp;models.TestType{
                Name: testType.Name,
        }

        res := p.db.Create(testTypeToCreate)
        if res.Error != nil </span><span class="cov0" title="0">{
                return nil, res.Error
        }</span>

        // Associate section types with the test type
        <span class="cov8" title="1">if len(sectionTypes) &gt; 0 </span><span class="cov8" title="1">{
                if err := p.db.Model(testTypeToCreate).Association("SectionTypes").Append(sectionTypes); err != nil </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("failed to associate section types: %w", err)
                }</span>
        }

        // Load the associations for the response
        <span class="cov8" title="1">if err := p.db.Preload("SectionTypes").First(testTypeToCreate, testTypeToCreate.ID).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to load test type with associations: %w", err)
        }</span>

        <span class="cov8" title="1">return testTypeToCreate, nil</span>
}

func (p *DbPlugin) CreateTest(ctx context.Context, test *models.Test, sectionsForCreate []models.SectionForCreate, testTypeName string) (*models.Test, error) <span class="cov8" title="1">{
        // Start a transaction
        tx := p.db.Begin()
        defer func() </span><span class="cov8" title="1">{
                if r := recover(); r != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                }</span>
        }()

        // Find the test type by name
        <span class="cov8" title="1">var testType models.TestType
        if err := tx.Where("name = ?", testTypeName).First(&amp;testType).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return nil, fmt.Errorf("test type '%s' not found: %w", testTypeName, err)
        }</span>

        // Create the test first
        <span class="cov8" title="1">testToCreate := &amp;models.Test{
                Name:        test.Name,
                TestTypeID:  testType.ID,
                FromTime:    test.FromTime,
                ToTime:      test.ToTime,
                Active:      test.Active,
                Description: test.Description,
        }

        if err := tx.Create(testToCreate).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return nil, fmt.Errorf("failed to create test: %w", err)
        }</span>

        // Create sections for the test
        <span class="cov8" title="1">for _, sectionData := range sectionsForCreate </span><span class="cov8" title="1">{
                // Find the section type by name
                var sectionType models.SectionType
                if err := tx.Where("name = ?", sectionData.SectionTypeName).First(&amp;sectionType).Error; err != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                        return nil, fmt.Errorf("section type '%s' not found: %w", sectionData.SectionTypeName, err)
                }</span>

                // Create the section
                <span class="cov8" title="1">section := &amp;models.Section{
                        Name:          sectionData.Name,
                        DisplayName:   sectionData.DisplayName,
                        TestID:        testToCreate.ID,
                        SectionTypeID: sectionType.ID,
                }

                if err := tx.Create(section).Error; err != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                        return nil, fmt.Errorf("failed to create section '%s': %w", sectionData.Name, err)
                }</span>
        }

        // Commit the transaction
        <span class="cov8" title="1">if err := tx.Commit().Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to commit transaction: %w", err)
        }</span>

        // Load the test with its sections, section types, and test type
        <span class="cov8" title="1">if err := p.db.Preload("TestType").Preload("Sections.SectionType").First(testToCreate, testToCreate.ID).Error; err != nil </span><span class="cov0" title="0">{
                return nil, fmt.Errorf("failed to load test with associations: %w", err)
        }</span>

        <span class="cov8" title="1">return testToCreate, nil</span>
}

func (p *DbPlugin) AddQuestionsToTest(ctx context.Context, testID uint, questionIDs []uint) error <span class="cov0" title="0">{
        // Start a transaction
        tx := p.db.Begin()
        defer func() </span><span class="cov0" title="0">{
                if r := recover(); r != nil </span><span class="cov0" title="0">{
                        tx.Rollback()
                }</span>
        }()

        // Verify the test exists
        <span class="cov0" title="0">var test models.Test
        if err := tx.First(&amp;test, testID).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                if errors.Is(err, gorm.ErrRecordNotFound) </span><span class="cov0" title="0">{
                        return fmt.Errorf("test with ID %d not found", testID)
                }</span>
                <span class="cov0" title="0">return fmt.Errorf("failed to find test: %w", err)</span>
        }

        // Verify all questions exist
        <span class="cov0" title="0">var questions []models.Question
        if err := tx.Where("id IN ?", questionIDs).Find(&amp;questions).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return fmt.Errorf("failed to find questions: %w", err)
        }</span>

        <span class="cov0" title="0">if len(questions) != len(questionIDs) </span><span class="cov0" title="0">{
                tx.Rollback()
                return errors.New("one or more questions not found")
        }</span>

        // Get test sections to add questions to appropriate sections
        <span class="cov0" title="0">var sections []models.Section
        if err := tx.Preload("SectionType").Where("test_id = ?", testID).Find(&amp;sections).Error; err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return fmt.Errorf("failed to find test sections: %w", err)
        }</span>

        <span class="cov0" title="0">if len(sections) == 0 </span><span class="cov0" title="0">{
                tx.Rollback()
                return errors.New("test has no sections to add questions to")
        }</span>

        // For simplicity, add all questions to the first section
        // In a real scenario, you might want to distribute questions based on topics or other criteria
        <span class="cov0" title="0">section := sections[0]

        // Add questions to the section using GORM's many-to-many association
        if err := tx.Model(&amp;section).Association("Questions").Append(questions); err != nil </span><span class="cov0" title="0">{
                tx.Rollback()
                return fmt.Errorf("failed to add questions to section: %w", err)
        }</span>

        // Commit the transaction
        <span class="cov0" title="0">if err := tx.Commit().Error; err != nil </span><span class="cov0" title="0">{
                return fmt.Errorf("failed to commit transaction: %w", err)
        }</span>

        <span class="cov0" title="0">return nil</span>
}
</pre>
		
		<pre class="file" id="file21" style="display: none">package db

import (
        "context"
        "ziaacademy-backend/internal/models"
        "ziaacademy-backend/internal/token"

        "golang.org/x/crypto/bcrypt"
)

func (p *DbPlugin) ValidateUserPassword(ctx context.Context, userEmail, pwd string) (uint, error) <span class="cov0" title="0">{
        var user models.User

        result := p.db.Where("email = ?", userEmail).First(&amp;user)
        if result.Error != nil </span><span class="cov0" title="0">{
                return 0, result.Error
        }</span>
        <span class="cov0" title="0">combined := pwd + token.SecretKeyStr
        err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(combined))
        if err != nil </span><span class="cov0" title="0">{
                return 0, err
        }</span>
        <span class="cov0" title="0">return user.ID, nil</span>
}

func (p *DbPlugin) UpdatePassword(ctx context.Context, userID uint,
        newPwd string) error <span class="cov0" title="0">{
        combined := newPwd + token.SecretKeyStr
        hash, err := bcrypt.GenerateFromPassword([]byte(combined), bcrypt.DefaultCost)
        if err != nil </span><span class="cov0" title="0">{
                return err
        }</span>

        <span class="cov0" title="0">var user models.User
        result := p.db.First(&amp;user, userID)
        if result.Error != nil </span><span class="cov0" title="0">{
                return result.Error
        }</span>
        <span class="cov0" title="0">user.PasswordHash = string(hash)
        result = p.db.Save(&amp;user)
        return result.Error</span>
}
</pre>
		
		<pre class="file" id="file22" style="display: none">// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admins": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new admin user with role \"Admin\"",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "admins"
                ],
                "summary": "Create Admin User",
                "parameters": [
                    {
                        "description": "Admin user details",
                        "name": "admin",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.AdminForCreate"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created",
                        "schema": {
                            "$ref": "#/definitions/models.User"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/chapters": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get chapters for a subject_id",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chapters"
                ],
                "summary": "Get Chapters",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Subject ID",
                        "name": "subject_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Chapter"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new chapter",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "chapters"
                ],
                "summary": "CreateChapters",
                "parameters": [
                    {
                        "description": "chapter details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ChapterForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Chapter"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/content": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get videos and studymaterial for given chapter",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "Get Content",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Chapter ID",
                        "name": "chapter_id",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Content"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/courses": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get courses for the logged in student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "courses"
                ],
                "summary": "Get Courses",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.CourseWithPurchased"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new course",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "courses"
                ],
                "summary": "CreateCourse",
                "parameters": [
                    {
                        "description": "course details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.CourseForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Course"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/enroll/{course_id}": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "enroll student in a course",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "EnrollInCourse",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "course ID to enroll in",
                        "name": "course_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Student"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/login": {
            "post": {
                "description": "login with email and password",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "login"
                ],
                "summary": "Login",
                "parameters": [
                    {
                        "description": "user_email and password",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.Credentials"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/questions": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get questions for given topic and difficulty",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "Get Questions",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Topic name",
                        "name": "topic",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Difficulty ()",
                        "name": "difficulty",
                        "in": "query",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Question"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new question",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "CreateQuestion",
                "parameters": [
                    {
                        "description": "question details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.QuestionForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Question"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/section-types": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new section type",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "CreateSectionType",
                "parameters": [
                    {
                        "description": "section type details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SectionTypeForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.SectionType"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/students": {
            "post": {
                "description": "create new student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "students"
                ],
                "summary": "CreateStudent",
                "parameters": [
                    {
                        "description": "student details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.StudentForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.CreatedStudentResponse"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/studymaterials": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "add a new material",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "library"
                ],
                "summary": "AddStudyMaterial",
                "parameters": [
                    {
                        "description": "material details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.MaterialForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.StudyMaterial"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/subjects": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "get subjects for logged in student",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "library"
                ],
                "summary": "Get Subjects",
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.Subject"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new subject",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "subjects"
                ],
                "summary": "CreateSubject",
                "parameters": [
                    {
                        "description": "subject details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.SubjectForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Subject"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/test-types": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new test type",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "CreateTestType",
                "parameters": [
                    {
                        "description": "test type details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TestTypeForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.TestType"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/tests": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new test of a given type",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "CreateTest",
                "parameters": [
                    {
                        "description": "test details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TestForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Test"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/tests/{test_id}/questions": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "add questions to a test",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "tests"
                ],
                "summary": "AddQuestionsToTest",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Test ID",
                        "name": "test_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "question IDs",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "integer"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/topics": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "create new topic for questions",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "questions"
                ],
                "summary": "CreateTopic",
                "parameters": [
                    {
                        "description": "topic details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.TopicForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Topic"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/users/password": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "update password for logged in user",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "users"
                ],
                "summary": "UpdatePassword",
                "parameters": [
                    {
                        "description": "new password",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.UpdatePassword"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK"
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        },
        "/videos": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "add a new video",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "library"
                ],
                "summary": "AddVideo",
                "parameters": [
                    {
                        "description": "video details",
                        "name": "item",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.VideoForCreate"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "$ref": "#/definitions/models.Video"
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "$ref": "#/definitions/http.HTTPError"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "gorm.DeletedAt": {
            "type": "object",
            "properties": {
                "time": {
                    "type": "string"
                },
                "valid": {
                    "description": "Valid is true if Time is not NULL",
                    "type": "boolean"
                }
            }
        },
        "http.HTTPError": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 400
                },
                "message": {
                    "type": "string",
                    "example": "status bad request"
                }
            }
        },
        "models.AdminForCreate": {
            "type": "object",
            "required": [
                "email",
                "full_name",
                "password",
                "phone_number"
            ],
            "properties": {
                "contact_address": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "full_name": {
                    "type": "string"
                },
                "password": {
                    "type": "string",
                    "minLength": 6
                },
                "phone_number": {
                    "type": "string"
                }
            }
        },
        "models.Chapter": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "studyMaterials": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudyMaterial"
                    }
                },
                "subjectID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                },
                "videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Video"
                    }
                }
            }
        },
        "models.ChapterForCreate": {
            "type": "object",
            "properties": {
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "subjectName": {
                    "type": "string"
                }
            }
        },
        "models.Content": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "pdfs": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.StudyMaterial"
                    }
                },
                "videos": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Video"
                    }
                }
            }
        },
        "models.Course": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number"
                },
                "durationInDays": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "subjects": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Subject"
                    }
                },
                "tests": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Test"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.CourseForCreate": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number"
                },
                "durationInDays": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "subjects": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SubjectForCreate"
                    }
                }
            }
        },
        "models.CourseWithPurchased": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "discount": {
                    "type": "number"
                },
                "durationInDays": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "price": {
                    "type": "integer"
                },
                "purchased": {
                    "type": "boolean"
                }
            }
        },
        "models.CreatedStudentResponse": {
            "type": "object",
            "properties": {
                "createdStudent": {
                    "$ref": "#/definitions/models.Student"
                },
                "token": {
                    "type": "string"
                }
            }
        },
        "models.Credentials": {
            "type": "object",
            "properties": {
                "password": {
                    "type": "string"
                },
                "user_email": {
                    "type": "string"
                }
            }
        },
        "models.Difficulty": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Question"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.MaterialForCreate": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.Question": {
            "type": "object",
            "properties": {
                "correctAnswer": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "difficulty": {
                    "$ref": "#/definitions/models.Difficulty"
                },
                "difficultyID": {
                    "type": "integer"
                },
                "fileUrl": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "imageUrl": {
                    "type": "string"
                },
                "questionType": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                },
                "topic": {
                    "$ref": "#/definitions/models.Topic"
                },
                "topicID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.QuestionForCreate": {
            "type": "object",
            "properties": {
                "correctAnswer": {
                    "type": "string"
                },
                "difficultyName": {
                    "type": "string"
                },
                "fileUrl": {
                    "type": "string"
                },
                "imageUrl": {
                    "type": "string"
                },
                "questionType": {
                    "type": "string"
                },
                "text": {
                    "type": "string"
                },
                "topicName": {
                    "type": "string"
                }
            }
        },
        "models.Section": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Question"
                    }
                },
                "sectionType": {
                    "$ref": "#/definitions/models.SectionType"
                },
                "sectionTypeID": {
                    "type": "integer"
                },
                "test": {
                    "$ref": "#/definitions/models.Test"
                },
                "testID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SectionForCreate": {
            "type": "object",
            "properties": {
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "sectionTypeName": {
                    "type": "string"
                }
            }
        },
        "models.SectionType": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "negativeMarks": {
                    "type": "number"
                },
                "positiveMarks": {
                    "type": "number"
                },
                "questionCount": {
                    "type": "integer"
                },
                "subject": {
                    "$ref": "#/definitions/models.Subject"
                },
                "subjectID": {
                    "type": "integer"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SectionTypeForCreate": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "negativeMarks": {
                    "type": "number"
                },
                "positiveMarks": {
                    "type": "number"
                },
                "questionCount": {
                    "type": "integer"
                },
                "subjectName": {
                    "type": "string"
                }
            }
        },
        "models.Student": {
            "type": "object",
            "properties": {
                "courses": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Course"
                    }
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "parent_email": {
                    "type": "string"
                },
                "parent_phone": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "user": {
                    "$ref": "#/definitions/models.User"
                },
                "userID": {
                    "type": "integer"
                }
            }
        },
        "models.StudentForCreate": {
            "type": "object",
            "properties": {
                "contactAddress": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "fullName": {
                    "type": "string"
                },
                "parent_email": {
                    "type": "string"
                },
                "parent_phone": {
                    "type": "string"
                },
                "phoneNumber": {
                    "type": "string"
                }
            }
        },
        "models.StudyMaterial": {
            "type": "object",
            "properties": {
                "chapterID": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "url": {
                    "type": "string"
                }
            }
        },
        "models.Subject": {
            "type": "object",
            "properties": {
                "chapters": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Chapter"
                    }
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.SubjectForCreate": {
            "type": "object",
            "properties": {
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.Test": {
            "type": "object",
            "properties": {
                "active": {
                    "type": "boolean"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "description": {
                    "type": "string"
                },
                "fromTime": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "sections": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Section"
                    }
                },
                "testType": {
                    "$ref": "#/definitions/models.TestType"
                },
                "testTypeID": {
                    "type": "integer"
                },
                "toTime": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TestForCreate": {
            "type": "object",
            "properties": {
                "description": {
                    "type": "string"
                },
                "fromTime": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "sections": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SectionForCreate"
                    }
                },
                "testTypeName": {
                    "type": "string"
                },
                "toTime": {
                    "type": "string"
                }
            }
        },
        "models.TestType": {
            "type": "object",
            "properties": {
                "createdAt": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "sectionTypes": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.SectionType"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TestTypeForCreate": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string"
                },
                "sectionTypeNames": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.Topic": {
            "type": "object",
            "properties": {
                "chapter": {
                    "$ref": "#/definitions/models.Chapter"
                },
                "chapterID": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "questions": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Question"
                    }
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.TopicForCreate": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "models.UpdatePassword": {
            "type": "object",
            "properties": {
                "new_password": {
                    "type": "string"
                }
            }
        },
        "models.User": {
            "type": "object",
            "properties": {
                "contactAddress": {
                    "type": "string"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "email": {
                    "type": "string"
                },
                "emailVerified": {
                    "type": "boolean"
                },
                "fullName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "passwordHash": {
                    "type": "string"
                },
                "phoneNumber": {
                    "type": "string"
                },
                "phoneVerified": {
                    "type": "boolean"
                },
                "role": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                }
            }
        },
        "models.Video": {
            "type": "object",
            "properties": {
                "chapterID": {
                    "type": "integer"
                },
                "createdAt": {
                    "type": "string"
                },
                "deletedAt": {
                    "$ref": "#/definitions/gorm.DeletedAt"
                },
                "displayName": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "name": {
                    "type": "string"
                },
                "updatedAt": {
                    "type": "string"
                },
                "videoUrl": {
                    "type": "string"
                },
                "viewCount": {
                    "type": "integer"
                }
            }
        },
        "models.VideoForCreate": {
            "type": "object",
            "properties": {
                "chapterName": {
                    "type": "string"
                },
                "displayName": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "videoUrl": {
                    "type": "string"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &amp;swag.Spec{
        Version:          "1.0",
        Host:             "************:443",
        BasePath:         "/api/",
        Schemes:          []string{},
        Title:            "ZIA Academy App",
        Description:      "Backend server for ZIA Academy.",
        InfoInstanceName: "swagger",
        SwaggerTemplate:  docTemplate,
        LeftDelim:        "{{",
        RightDelim:       "}}",
}

func init() <span class="cov8" title="1">{
        swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}</span>
</pre>
		
		<pre class="file" id="file23" style="display: none">package middleware

import (
        "net/http"
        "ziaacademy-backend/internal/token"

        "github.com/gin-gonic/gin"
)

func JwtAuthMiddleware() gin.HandlerFunc <span class="cov8" title="1">{
        return func(c *gin.Context) </span><span class="cov0" title="0">{
                err := token.TokenValid(c)
                if err != nil </span><span class="cov0" title="0">{
                        c.String(http.StatusUnauthorized, "Unauthorized")
                        c.Abort()
                        return
                }</span>
                <span class="cov0" title="0">c.Next()</span>
        }
}
</pre>
		
		<pre class="file" id="file24" style="display: none">package models

import (
        "strings"
        "time"

        "gorm.io/gorm"
)

type User struct {
        gorm.Model
        FullName       string `gorm:"not null"`
        Email          string `gorm:"not null;unique_index"`
        PhoneNumber    string `gorm:"not null;unique_index"`
        PasswordHash   string
        Role           string
        ContactAddress string
        EmailVerified  bool
        PhoneVerified  bool
}

type UserForCreate struct {
        FullName       string
        Email          string
        PhoneNumber    string
        ContactAddress string
}

type Student struct {
        gorm.Model
        ParentPhone string `json:"parent_phone"`
        ParentEmail string `json:"parent_email"`
        UserID      uint
        User        User
        Courses     []Course `gorm:"many2many:students_courses;"`
}

type StudentForCreate struct {
        UserForCreate
        ParentPhone string `json:"parent_phone"`
        ParentEmail string `json:"parent_email"`
}

type Video struct {
        gorm.Model
        Name        string
        DisplayName string
        VideoUrl    string
        ViewCount   uint
        ChapterID   uint
}

type VideoForCreate struct {
        Name        string
        DisplayName string
        VideoUrl    string
        ChapterName string
}

type StudyMaterial struct {
        gorm.Model
        Name        string
        DisplayName string
        Url         string
        ChapterID   uint
}

type MaterialForCreate struct {
        Name        string
        DisplayName string
        Url         string
        ChapterName string
}

type Content struct {
        ChapterName string
        Videos      []Video
        Pdfs        []StudyMaterial
}

type Chapter struct {
        gorm.Model
        Name           string `gorm:"unique;not null"`
        DisplayName    string
        Videos         []Video
        StudyMaterials []StudyMaterial
        SubjectID      uint
}

type ChapterForCreate struct {
        Name        string
        DisplayName string
        SubjectName string
}

type Subject struct {
        gorm.Model
        Name        string `gorm:"unique;not null"`
        DisplayName string
        Chapters    []Chapter
}

type SubjectForCreate struct {
        Name        string
        DisplayName string
}

type CourseForCreate struct {
        Name           string
        Description    string
        Price          int
        Discount       float32
        DurationInDays int
        Subjects       []SubjectForCreate
}

type Course struct {
        gorm.Model
        Name           string `gorm:"unique;not null"`
        Description    string
        Price          int
        Discount       float32
        DurationInDays int
        Subjects       []Subject `gorm:"many2many:courses_subjects;"`
        Tests          []Test    `gorm:"many2many:courses_tests;"`
}

type CourseWithPurchased struct {
        Name           string
        Description    string
        Price          int
        Discount       float32
        DurationInDays int
        Purchased      bool
}

type Credentials struct {
        UserEmail string `json:"user_email"`
        Password  string `json:"password"`
}

type UpdatePassword struct {
        NewPassword string `json:"new_password"`
}

type AdminForCreate struct {
        FullName       string `json:"full_name" binding:"required"`
        Email          string `json:"email" binding:"required,email"`
        PhoneNumber    string `json:"phone_number" binding:"required"`
        ContactAddress string `json:"contact_address"`
        Password       string `json:"password" binding:"required,min=6"`
}

func (us *User) Sanitize() <span class="cov8" title="1">{
        us.FullName = strings.TrimSpace(us.FullName)
        us.Email = strings.TrimSpace(us.Email)
        us.PhoneNumber = strings.TrimSpace(us.PhoneNumber)
        us.ContactAddress = strings.TrimSpace(us.ContactAddress)
}</span>

type Difficulty struct {
        gorm.Model
        Name      string `gorm:"unique;not null"`
        Questions []Question
}

type Topic struct {
        gorm.Model
        Name      string  `gorm:"unique;not null"`
        ChapterID uint    `gorm:"not null"`
        Chapter   Chapter `gorm:"foreignKey:ChapterID"`
        Questions []Question
}

type TopicForCreate struct {
        Name        string
        ChapterName string
}

type Question struct {
        gorm.Model
        Text          string     `gorm:"not null"`
        TopicID       uint       `gorm:"not null"`
        DifficultyID  uint       `gorm:"not null"`
        Topic         Topic      `gorm:"foreignKey:TopicID"`
        Difficulty    Difficulty `gorm:"foreignKey:DifficultyID"`
        QuestionType  string
        ImageUrl      string
        FileUrl       string
        CorrectAnswer string
}

type QuestionForCreate struct {
        Text           string
        TopicName      string
        DifficultyName string
        QuestionType   string
        ImageUrl       string
        FileUrl        string
        CorrectAnswer  string
}

type SectionType struct {
        gorm.Model
        Name          string `gorm:"unique;not null"`
        SubjectID     uint   `gorm:"not null"`
        Subject       Subject
        QuestionCount int     `gorm:"not null"`
        PositiveMarks float64 `gorm:"not null"`
        NegativeMarks float64 `gorm:"not null"`
}

type SectionTypeForCreate struct {
        Name          string
        SubjectName   string
        QuestionCount int
        PositiveMarks float64
        NegativeMarks float64
}

type TestType struct {
        ID           uint          `gorm:"primaryKey"`
        Name         string        `gorm:"unique;not null"`
        SectionTypes []SectionType `gorm:"many2many:test_type_section_types;"`
        CreatedAt    time.Time
        UpdatedAt    time.Time
}

type TestTypeForCreate struct {
        Name             string
        SectionTypeNames []string
}

type Test struct {
        gorm.Model
        Name        string `gorm:"not null"`
        TestTypeID  uint   `gorm:"not null"`
        TestType    TestType
        Sections    []Section
        FromTime    time.Time
        ToTime      time.Time
        Active      bool
        Description string
}

type TestForCreate struct {
        Name         string
        TestTypeName string
        Sections     []SectionForCreate
        Description  string
        FromTime     time.Time
        ToTime       time.Time
}

type Section struct {
        gorm.Model
        Name          string     `gorm:"unique not null"`
        DisplayName   string     `gorm:"not null"`
        TestID        uint       `gorm:"not null"`
        Test          Test       `gorm:"foreignKey:TestID"`
        Questions     []Question `gorm:"many2many:sections_questions;"`
        SectionTypeID uint       `gorm:"not null"`
        SectionType   SectionType
}

type SectionForCreate struct {
        Name            string
        DisplayName     string
        SectionTypeName string
}

type Option struct {
        gorm.Model

        QuestionID     uint     `gorm:"not null"`              // Foreign key to Questions
        Question       Question `gorm:"foreignKey:QuestionID"` // GORM association
        OptionText     string   `gorm:"type:text;not null"`    // Option content
        OptionImageURL *string  `gorm:"type:text"`             // Optional image URL
        IsCorrect      bool     `gorm:"default:false"`         // Correctness flag
}

type TestResponse struct {
        gorm.Model

        StudentID uint    `gorm:"not null"`             // FK to students
        Student   Student `gorm:"foreignKey:StudentID"` // Assumes you have a Student model

        TestID uint `gorm:"not null"` // FK to tests
        Test   Test `gorm:"foreignKey:TestID"`

        QuestionID uint     `gorm:"not null"` // FK to questions
        Question   Question `gorm:"foreignKey:QuestionID"`

        SelectedOptionIDs []int   `gorm:"type:integer[]"` // PostgreSQL array type (use pgx or pq)
        ResponseText      *string `gorm:"type:text"`      // Nullable for text answers
        CalculatedScore   *int    `gorm:"default:null"`   // Nullable, can be set after evaluation
        IsCorrect         bool    `gorm:"default:false"`  // Automatically evaluated
}

type Response struct {
        gorm.Model

        CommentID uint    `gorm:"not null"` // FK to Comment
        Comment   Comment `gorm:"foreignKey:CommentID"`

        UserID uint `gorm:"not null"` // FK to User
        User   User `gorm:"foreignKey:UserID"`

        ResponseText string `gorm:"type:text;not null"`
}

type Comment struct {
        gorm.Model

        UserID uint `gorm:"not null"`          // FK to users
        User   User `gorm:"foreignKey:UserID"` // Assumes a User model exists

        VideoID *uint  `gorm:"default:null"` // Optional: comments can belong to a video
        Video   *Video `gorm:"foreignKey:VideoID"`

        MaterialID *uint          `gorm:"default:null"` // Optional: or to a study material
        Material   *StudyMaterial `gorm:"foreignKey:MaterialID"`

        CommentText string `gorm:"type:text;not null"`

        Responses []Response `gorm:"constraint:OnDelete:CASCADE"` // Replies to this comment
}

type StudentTestMark struct {
        StudentID          int        `gorm:"primaryKey;not null"`
        TestID             int        `gorm:"primaryKey;not null"`
        TotalPositiveMarks int        `gorm:"default:0;not null"`
        TotalNegativeMarks int        `gorm:"default:0;not null"`
        FinalMarks         int        `gorm:"-&gt;;type:integer;not null"` // Read-only; generated column
        CreatedAt          time.Time  `gorm:"autoCreateTime"`
        UpdatedAt          time.Time  `gorm:"autoUpdateTime"`
        DeletedAt          *time.Time `gorm:"index"`

        Student Student `gorm:"foreignKey:StudentID;constraint:OnDelete:CASCADE"`
        Test    Test    `gorm:"foreignKey:TestID;constraint:OnDelete:CASCADE"`
}

type CreatedStudentResponse struct {
        Token          string  `json:"token"`
        CreatedStudent Student `json:"createdStudent"`
}
</pre>
		
		<pre class="file" id="file25" style="display: none">package token

import (
        "fmt"
        "log/slog"
        "strconv"
        "strings"
        "time"

        "github.com/gin-gonic/gin"
        jwt "github.com/golang-jwt/jwt/v4"
)

var SecretKeyStr = "your-secret-key"
var SecretKey = []byte("your-secret-key")

// JWT claims struct
type Claims struct {
        UserID uint `json:"user_id"`
        jwt.RegisteredClaims
}

// GenerateJWT creates a JWT token for the authenticated user
func GenerateJWT(userID uint) (string, error) <span class="cov8" title="1">{
        // Define expiration time (e.g., 1 hour)
        expirationTime := time.Now().Add(time.Hour * 1)

        // Create the JWT claims
        claims := &amp;Claims{
                UserID: userID,
                RegisteredClaims: jwt.RegisteredClaims{
                        Issuer:    "GoAuthAPI",
                        ExpiresAt: jwt.NewNumericDate(expirationTime),
                },
        }

        // Create a new JWT token
        token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

        // Sign the token with the secret key
        signedToken, err := token.SignedString(SecretKey)
        if err != nil </span><span class="cov0" title="0">{
                return "", err
        }</span>

        <span class="cov8" title="1">return signedToken, nil</span>
}

func TokenValid(c *gin.Context) error <span class="cov0" title="0">{
        tokenString := ExtractToken(c)
        parsedToken, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) </span><span class="cov0" title="0">{
                if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
                }</span>
                <span class="cov0" title="0">return SecretKey, nil</span>
        })
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                slog.Error("Failed to parse token", "error", err.Error())
                return err
        }</span>
        // Optionally extract claims
        <span class="cov0" title="0">if claims, ok := parsedToken.Claims.(jwt.MapClaims); ok </span><span class="cov0" title="0">{
                c.Set("userID", claims["user_id"]) // set in context for use in handlers
        }</span>
        <span class="cov0" title="0">return nil</span>
}

func ExtractToken(c *gin.Context) string <span class="cov0" title="0">{
        token := c.Query("token")
        if token != "" </span><span class="cov0" title="0">{
                return token
        }</span>
        <span class="cov0" title="0">bearerToken := c.Request.Header.Get("Authorization")
        if len(strings.Split(bearerToken, " ")) == 2 </span><span class="cov0" title="0">{
                return strings.Split(bearerToken, " ")[1]
        }</span>
        <span class="cov0" title="0">return ""</span>
}

func ExtractTokenID(c *gin.Context) (uint, error) <span class="cov0" title="0">{

        tokenString := ExtractToken(c)
        token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) </span><span class="cov0" title="0">{
                if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok </span><span class="cov0" title="0">{
                        return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
                }</span>
                <span class="cov0" title="0">return SecretKey, nil</span>
        })
        <span class="cov0" title="0">if err != nil </span><span class="cov0" title="0">{
                slog.Error("failed to parse token", "error", err.Error())
                return 0, err
        }</span>
        <span class="cov0" title="0">claims, ok := token.Claims.(jwt.MapClaims)
        if ok &amp;&amp; token.Valid </span><span class="cov0" title="0">{
                uid, err := strconv.ParseUint(fmt.Sprintf("%.0f", claims["user_id"]), 10, 32)
                if err != nil </span><span class="cov0" title="0">{
                        slog.Error("failed to parse uint", "error", err.Error())
                        return 0, err
                }</span>
                <span class="cov0" title="0">return uint(uid), nil</span>
        }
        <span class="cov0" title="0">return 0, nil</span>
}
</pre>
		
		</div>
	</body>
	<script>
	(function() {
		var files = document.getElementById('files');
		var visible;
		files.addEventListener('change', onChange, false);
		function select(part) {
			if (visible)
				visible.style.display = 'none';
			visible = document.getElementById(part);
			if (!visible)
				return;
			files.value = part;
			visible.style.display = 'block';
			location.hash = part;
		}
		function onChange() {
			select(files.value);
			window.scrollTo(0, 0);
		}
		if (location.hash != "") {
			select(location.hash.substr(1));
		}
		if (!visible) {
			select("file0");
		}
	})();
	</script>
</html>
