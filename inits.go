package main

import (
	"context"
	"os"

	"gorm.io/gorm"

	xhttp "ziaacademy-backend/cmd/server/http"
	"ziaacademy-backend/db"

	"ziaacademy-backend/internal/configs"
)

func start(
	ctx context.Context,
	gormDb *gorm.DB,
	cfgs *configs.Configs,
	logFile *os.File,
	sslCert, sslKey string,
	fatalErr chan<- error,
) {
	_ = ctx

	server := db.NewServer(gormDb)
	_ = xhttp.StartServer(server, cfgs.Http, logFile, sslCert, sslKey, fatalErr)
}
