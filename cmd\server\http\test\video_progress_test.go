package test

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestVideoProgressAPI(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping video progress API test")
		return
	}

	// Clean up before test
	timestamp := fmt.Sprintf("%d", time.Now().Unix())
	testSubjectName := "Video Progress Test Subject " + timestamp
	testChapterName := "Video Progress Test Chapter " + timestamp
	testVideoName := "test_video_progress_" + timestamp

	// Create test data
	subject := models.Subject{
		Name:        testSubjectName,
		DisplayName: "Video Progress Test Subject",
	}
	db.Create(&subject)

	chapter := models.Chapter{
		Name:        testChapterName,
		DisplayName: "Video Progress Test Chapter",
		SubjectID:   subject.ID,
	}
	db.Create(&chapter)

	video := models.Video{
		Name:        testVideoName,
		DisplayName: "Test Video for Progress",
		VideoUrl:    "https://example.com/test_video.mp4",
		ViewCount:   0,
		ChapterID:   chapter.ID,
	}
	db.Create(&video)

	// Create test student with authentication token
	studentPayload := models.StudentForCreate{
		UserForCreate: models.UserForCreate{
			FullName:       "Test Student Progress",
			Email:          "test.progress." + timestamp + "@example.com",
			PhoneNumber:    "1234567890" + timestamp[len(timestamp)-3:],
			ContactAddress: "Test Address",
		},
		ParentPhone: "9876543210",
		ParentEmail: "parent.progress." + timestamp + "@example.com",
		Institute:   "Test Institute",
		Class:       "12th",
		Stream:      "IIT-JEE",
		CityOrTown:  "Test City",
		State:       "Test State",
		Password:    "testpassword123",
	}

	studentResp := requestExecutionHelper(http.MethodPost, "/api/students", studentPayload)
	assert.Equal(t, http.StatusOK, studentResp.Code)

	var studentResponse models.CreatedStudentResponse
	err := json.Unmarshal(studentResp.Body.Bytes(), &studentResponse)
	assert.Nil(t, err)

	studentToken := studentResponse.Token
	assert.NotEmpty(t, studentToken)

	testStudent := studentResponse.Student

	// Test 1: Update video progress (create new)
	t.Run("UpdateVideoProgress_Create", func(t *testing.T) {
		progressData := models.VideoProgressForUpdate{
			VideoID:         video.ID,
			ProgressSeconds: 120,
			ProgressPercent: 25.5,
			VideoDuration:   480,
			IsCompleted:     false,
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/video-progress", progressData, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var response map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		assert.Nil(t, err)

		assert.Equal(t, "Video progress updated successfully", response["message"])
	})

	// Test 2: Update video progress (update existing)
	t.Run("UpdateVideoProgress_Update", func(t *testing.T) {
		progressData := models.VideoProgressForUpdate{
			VideoID:         video.ID,
			ProgressSeconds: 360,
			ProgressPercent: 75.0,
			VideoDuration:   480,
			IsCompleted:     false,
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/video-progress", progressData, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var response map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		assert.Nil(t, err)

		assert.Equal(t, "Video progress updated successfully", response["message"])
	})

	// Test 3: Complete video
	t.Run("UpdateVideoProgress_Complete", func(t *testing.T) {
		progressData := models.VideoProgressForUpdate{
			VideoID:         video.ID,
			ProgressSeconds: 480,
			ProgressPercent: 100.0,
			VideoDuration:   480,
			IsCompleted:     true,
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/video-progress", progressData, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var response map[string]interface{}
		err := json.Unmarshal(resp.Body.Bytes(), &response)
		assert.Nil(t, err)

		assert.Equal(t, "Video progress updated successfully", response["message"])
	})

	// Test 4: Verify progress integration in content API
	t.Run("GetContentWithProgress", func(t *testing.T) {
		resp := authenticatedRequestHelper(http.MethodGet, "/api/content", nil, studentToken)
		assert.Equal(t, http.StatusOK, resp.Code)

		var content models.ContentResponseWithProgress
		err := json.Unmarshal(resp.Body.Bytes(), &content)
		assert.Nil(t, err)

		assert.GreaterOrEqual(t, len(content.Subjects), 1, "Should have at least one subject")

		// Find our test subject
		testSubject, exists := content.Subjects[testSubjectName]
		assert.True(t, exists, "Test subject should exist in content response")

		assert.NotNil(t, testSubject, "Should find test subject")
		assert.GreaterOrEqual(t, len(testSubject.Videos), 1, "Subject should have at least one video")

		// Find our test video with progress
		var testVideoWithProgress *models.VideoForGet
		for _, videoWithProgress := range testSubject.Videos {
			if videoWithProgress.Name == testVideoName {
				testVideoWithProgress = &videoWithProgress
				break
			}
		}

		assert.NotNil(t, testVideoWithProgress, "Should find test video with progress")
		assert.Equal(t, testVideoName, testVideoWithProgress.Name)
		assert.Equal(t, "Test Video for Progress", testVideoWithProgress.DisplayName)
		assert.Equal(t, 480, testVideoWithProgress.ProgressSeconds)
		assert.Equal(t, 100.0, testVideoWithProgress.ProgressPercent)
		assert.Equal(t, 480, testVideoWithProgress.VideoDuration)
		assert.True(t, testVideoWithProgress.IsCompleted)
		assert.NotNil(t, testVideoWithProgress.LastWatchedAt)
	})

	// Test 8: Invalid video progress data
	t.Run("UpdateVideoProgress_InvalidData", func(t *testing.T) {
		progressData := models.VideoProgressForUpdate{
			VideoID:         video.ID,
			ProgressSeconds: -10,   // Invalid negative progress
			ProgressPercent: 150.0, // Invalid percentage > 100
			VideoDuration:   0,     // Invalid duration
			IsCompleted:     false,
		}

		resp := authenticatedRequestHelper(http.MethodPut, "/api/video-progress", progressData, studentToken)
		assert.Equal(t, http.StatusBadRequest, resp.Code)
	})

	// Cleanup
	db.Exec("DELETE FROM video_progresses WHERE video_id = ?", video.ID)
	db.Exec("DELETE FROM videos WHERE id = ?", video.ID)
	db.Exec("DELETE FROM chapters WHERE id = ?", chapter.ID)
	db.Exec("DELETE FROM subjects WHERE id = ?", subject.ID)

	// Get the user ID from the student record before deleting
	var studentRecord models.Student
	db.First(&studentRecord, testStudent.ID)
	db.Exec("DELETE FROM students WHERE id = ?", testStudent.ID)
	db.Exec("DELETE FROM users WHERE id = ?", studentRecord.UserID)
}
