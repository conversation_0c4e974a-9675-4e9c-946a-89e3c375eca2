package test

import (
	"net/http"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCommentEndpointsRegistered(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping comments test")
		return
	}

	// Test that comment endpoints are registered and return authentication errors
	// rather than 404 (not found), which proves they exist

	// Test POST /api/comments endpoint exists
	commentInput := models.CommentForCreate{
		CommentText: "Test comment",
		VideoID:     func() *uint { v := uint(1); return &v }(),
	}
	rr := requestExecutionHelper("POST", "/api/comments", commentInput)
	// Should return 400 (bad request due to missing auth) not 404 (not found)
	assert.NotEqual(t, http.StatusNotFound, rr.Code, "POST /api/comments endpoint should exist")

	// Test POST /api/responses endpoint exists
	responseInput := models.ResponseForCreate{
		CommentID:    1,
		ResponseText: "Test response",
	}
	rr2 := requestExecutionHelper("POST", "/api/responses", responseInput)
	// Should return 400 (bad request due to missing auth) not 404 (not found)
	assert.NotEqual(t, http.StatusNotFound, rr2.Code, "POST /api/responses endpoint should exist")

	// Test GET /api/comments endpoint exists
	rr3 := requestExecutionHelper("GET", "/api/comments?video_id=1", nil)
	// Should return 400 (bad request due to missing auth) not 404 (not found)
	assert.NotEqual(t, http.StatusNotFound, rr3.Code, "GET /api/comments endpoint should exist")
}

func TestCommentValidationRules(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping comments test")
		return
	}

	// Test validation rules for comment creation
	// These should return 400 (bad request) for validation errors

	// Test 1: Comment with both video_id and material_id (should fail validation)
	videoID := uint(1)
	materialID := uint(1)
	commentInput := models.CommentForCreate{
		CommentText: "This should fail validation",
		VideoID:     &videoID,
		MaterialID:  &materialID,
	}
	rr := requestExecutionHelper("POST", "/api/comments", commentInput)
	assert.Equal(t, http.StatusBadRequest, rr.Code, "Should reject comment with both video_id and material_id")

	// Test 2: Comment with neither video_id nor material_id (should fail validation)
	commentInput2 := models.CommentForCreate{
		CommentText: "This should also fail validation",
	}
	rr2 := requestExecutionHelper("POST", "/api/comments", commentInput2)
	assert.Equal(t, http.StatusBadRequest, rr2.Code, "Should reject comment with neither video_id nor material_id")
}
