-- Migration to add video_progresses table for tracking student video watch progress
-- Run this script to update existing database schema

CREATE TABLE IF NOT EXISTS video_progresses (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL,
    video_id INTEGER NOT NULL,
    progress_seconds INTEGER NOT NULL DEFAULT 0,
    progress_percent DECIMAL(5,2) NOT NULL DEFAULT 0.00 CHECK (progress_percent >= 0 AND progress_percent <= 100),
    video_duration INTEGER NOT NULL DEFAULT 0,
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    last_watched_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    deleted_at TIMESTAMPTZ,
    
    -- Create unique constraint to ensure one progress record per student per video
    CONSTRAINT unique_student_video UNIQUE (student_id, video_id),
    
    -- Foreign key constraints
    CONSTRAINT fk_video_progresses_student_id 
        FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    CONSTRAINT fk_video_progresses_video_id 
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_video_progresses_student_id ON video_progresses(student_id);
CREATE INDEX IF NOT EXISTS idx_video_progresses_video_id ON video_progresses(video_id);
CREATE INDEX IF NOT EXISTS idx_video_progresses_last_watched_at ON video_progresses(last_watched_at);
CREATE INDEX IF NOT EXISTS idx_video_progresses_is_completed ON video_progresses(is_completed);

-- Add comments for documentation
COMMENT ON TABLE video_progresses IS 'Tracks video watch progress for each student';
COMMENT ON COLUMN video_progresses.student_id IS 'Reference to the student who watched the video';
COMMENT ON COLUMN video_progresses.video_id IS 'Reference to the video being watched';
COMMENT ON COLUMN video_progresses.progress_seconds IS 'How far the student has watched in seconds';
COMMENT ON COLUMN video_progresses.progress_percent IS 'Progress as percentage (0-100)';
COMMENT ON COLUMN video_progresses.video_duration IS 'Total duration of the video in seconds';
COMMENT ON COLUMN video_progresses.is_completed IS 'Whether the student has completed watching the video';
COMMENT ON COLUMN video_progresses.last_watched_at IS 'When the student last watched this video';
