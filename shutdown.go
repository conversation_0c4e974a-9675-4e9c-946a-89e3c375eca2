package main

import (
	"context"
	"log/slog"
	"sync"
	"time"

	"gorm.io/gorm"
)

func shutdown(
	shutdownGraceperiod time.Duration,
	probeInterval time.Duration,
	db *gorm.DB,
) {

	// the time should be decided based on the grace period allowed for shutdown.
	// e.g. for Kubernetes terminationGracePeriodSeconds, https://kubernetes.io/docs/concepts/containers/container-lifecycle-hooks/
	ctx, cancel := context.WithTimeout(context.Background(), shutdownGraceperiod)
	defer cancel()

	/*
		When a server begins its shutdown process, it first signals Kubernetes (or any other prober)
		by changing its readiness state to "not ready". This ensures that the server stops receiving new traffic.

		However, there is typically a delay before Kubernetes detects this readiness change because
		it relies on periodic probing to check the status. During this delay, Kubernetes may still
		route new requests to the server, unaware that shutdown has initiated.

		To handle this, a deliberate pause is introduced between changing the readiness state to
		"not ready" and initiating the full shutdown. This pause should be longer than Kubernetes'
		readiness probe interval. This way, Kubernetes has enough time to notice the readiness change
		and stop sending new requests before the server begins rejecting them.
	*/
	// in this case, the Kuberenetes probe interval is assumed to be 2 seconds
	time.Sleep(probeInterval)
	slog.Info("initiating shutdown")
	shutdownDependenciesAndServices(ctx, db)
}

func shutdownDependenciesAndServices(
	ctx context.Context,
	db *gorm.DB,
) {
	wgroup := &sync.WaitGroup{}

	// after all the APIs of the application are shutdown (e.g. HTTP, gRPC, Pubsub listener etc.)
	// we should close connections to dependencies like database, cache etc.
	// This should only be done after the APIs are shutdown completely
	if db != nil {
		wgroup.Add(1)
		go func() {
			defer wgroup.Done()
			dbInstance, _ := db.DB()
			_ = dbInstance.Close()
		}()
	}

	wgroup.Wait()
}
