package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

func (p *DbPlugin) GetChapters(ctx context.Context, subjectID uint) ([]models.Chapter, error) {
	start := time.Now()
	slog.Debug("Retrieving chapters for subject", "subject_id", subjectID)

	var chapters []models.Chapter

	// Retrieve list of chapters for the given subject ID
	if err := p.db.Where("subject_id = ?", subjectID).
		Find(&chapters).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve chapters for subject",
			"subject_id", subjectID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Chapters retrieved successfully",
		"subject_id", subjectID,
		"chapter_count", len(chapters),
		"duration_ms", duration.Milliseconds(),
	)
	return chapters, nil
}

func (p *DbPlugin) CreateChapter(ctx context.Context, chapter *models.Chapter, subjectName string) (*models.Chapter, error) {
	start := time.Now()
	slog.Info("Creating chapter",
		"chapter_name", chapter.Name,
		"chapter_display_name", chapter.DisplayName,
		"subject_name", subjectName,
	)

	// Find the subject by name
	var subject models.Subject
	if err := p.db.Where("name = ?", subjectName).First(&subject).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Subject not found for chapter creation",
			"chapter_name", chapter.Name,
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("subject '%s' not found: %w", subjectName, err)
	}

	// Set the subject ID
	chapter.SubjectID = subject.ID

	slog.Debug("Creating chapter with subject association",
		"chapter_name", chapter.Name,
		"subject_id", subject.ID,
		"subject_name", subjectName,
	)

	res := p.db.Create(chapter)
	if res.Error != nil {
		duration := time.Since(start)
		slog.Error("Failed to create chapter",
			"chapter_name", chapter.Name,
			"subject_name", subjectName,
			"subject_id", subject.ID,
			"error", res.Error.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, res.Error
	}

	// Load the chapter with subject association
	if err := p.db.Preload("Subject").First(chapter, chapter.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load chapter with subject association",
			"chapter_id", chapter.ID,
			"chapter_name", chapter.Name,
			"subject_name", subjectName,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to load chapter with subject: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Chapter created successfully",
		"chapter_id", chapter.ID,
		"chapter_name", chapter.Name,
		"chapter_display_name", chapter.DisplayName,
		"subject_name", subjectName,
		"subject_id", subject.ID,
		"duration_ms", duration.Milliseconds(),
	)
	return chapter, nil
}
