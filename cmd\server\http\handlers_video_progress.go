package http

import (
	"fmt"
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
)

// UpdateVideoProgress godoc
//
//	@Summary		Update Video Progress
//	@Description	Update or create video watch progress for the authenticated student
//	@Security       BearerAuth
//	@Tags			video-progress
//	@Accept			json
//	@Produce		json
//	@Param			progress	body		models.VideoProgressForUpdate	true	"Video progress data"
//	@Success		200
//	@Failure		400			{object}	HTTPError
//	@Failure		401			{object}	HTTPError
//	@Failure		404			{object}	HTTPError
//	@Failure		500			{object}	HTTPError
//	@Router			/video-progress [put]
func (h *Handlers) UpdateVideoProgress(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	// Get student ID from JWT token
	userID, exists := ctx.Get("userID")
	if !exists {
		slog.Error("UpdateVideoProgress failed - userID not found in context",
			"client_ip", clientIP,
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Get student ID from user ID (convert from float64 to uint)
	userIDFloat, ok := userID.(float64)
	if !ok {
		slog.Error("UpdateVideoProgress failed - invalid userID type",
			"client_ip", clientIP,
			"userID_type", fmt.Sprintf("%T", userID),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
		return
	}
	studentID, err := h.db.GetStudentIDByUserID(ctx.Request.Context(), uint(userIDFloat))
	if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateVideoProgress failed - student not found",
			"client_ip", clientIP,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Student not found"})
		return
	}

	var progressData models.VideoProgressForUpdate
	if err := ctx.ShouldBindJSON(&progressData); err != nil {
		duration := time.Since(start)
		slog.Error("UpdateVideoProgress failed - invalid request body",
			"client_ip", clientIP,
			"student_id", studentID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Info("UpdateVideoProgress request started",
		"client_ip", clientIP,
		"student_id", studentID,
		"video_id", progressData.VideoID,
		"progress_seconds", progressData.ProgressSeconds,
		"progress_percent", progressData.ProgressPercent,
	)

	err = h.db.UpdateVideoProgress(ctx.Request.Context(), studentID, &progressData)
	if err != nil {
		duration := time.Since(start)
		slog.Error("UpdateVideoProgress failed - database error",
			"client_ip", clientIP,
			"student_id", studentID,
			"video_id", progressData.VideoID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	duration := time.Since(start)
	slog.Info("UpdateVideoProgress successful",
		"client_ip", clientIP,
		"student_id", studentID,
		"video_id", progressData.VideoID,
		"progress_percent", progressData.ProgressPercent,
		"is_completed", progressData.IsCompleted,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"message": "Video progress updated successfully"})
}
