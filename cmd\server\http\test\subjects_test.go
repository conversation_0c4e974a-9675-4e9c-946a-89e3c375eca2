package test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"
	dbPkg "ziaacademy-backend/db"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateSubject(t *testing.T) {
	// Prepare request payload
	chapters := []models.Chapter{
		{Name: "My Fav",
			DisplayName: "Chemical Bonding"},
	}
	newSubject := map[string]any{
		"Name":        "Chemistry QQ",
		"DisplayName": "Chemistry",
		"Chapters":    chapters,
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)

	// Check status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Parse response if your API returns the created subject
	var created models.Subject
	err := json.Unmarshal(rr.Body.Bytes(), &created)
	assert.Nil(t, err)
	assert.Equal(t, "Chemistry QQ", created.Name)

	// Verify it was persisted in the database
	var fromDB models.Subject
	err = db.First(&fromDB, created.ID).Error
	assert.Nil(t, err)
	assert.Equal(t, "Chemistry QQ", fromDB.Name)

	// Cleanup
	db.Exec("DELETE FROM subjects WHERE name = ?", "Chemistry QQ")
}

func TestGetSubjectsRoleBased(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping subjects test")
		return
	}

	// Use timestamp to ensure unique names
	timestamp := fmt.Sprintf("%d", time.Now().UnixNano())

	// Clean up before test
	adminEmail := "testadmin" + timestamp + "@example.com"
	studentEmail := "teststudent" + timestamp + "@example.com"
	subjectName1 := "Test Subject 1 " + timestamp
	subjectName2 := "Test Subject 2 " + timestamp
	courseName := "Test Course " + timestamp

	if db != nil {
		db.Exec("DELETE FROM users WHERE email IN (?, ?)", adminEmail, studentEmail)
		db.Exec("DELETE FROM subjects WHERE name IN (?, ?)", subjectName1, subjectName2)
		db.Exec("DELETE FROM courses WHERE name = ?", courseName)
	}

	// Step 1: Create test subjects
	subject1 := models.Subject{Name: subjectName1, DisplayName: "Test Subject 1"}
	subject2 := models.Subject{Name: subjectName2, DisplayName: "Test Subject 2"}

	result := db.Create(&subject1)
	assert.Nil(t, result.Error, "Should be able to create test subject 1")

	result = db.Create(&subject2)
	assert.Nil(t, result.Error, "Should be able to create test subject 2")

	// Step 2: Create a test course with one subject
	course := models.Course{
		Name:        courseName,
		Description: "Test course for subjects",
		IsFree:      true,
		CourseType:  "IIT-JEE",
		Subjects:    []models.Subject{subject1}, // Only subject1 is in the course
	}
	result = db.Create(&course)
	assert.Nil(t, result.Error, "Should be able to create test course")

	// Step 3: Create an admin user
	adminUser := models.User{
		FullName:    "Test Admin",
		Email:       adminEmail,
		PhoneNumber: "123456788",
		Role:        "Admin",
	}
	result = db.Create(&adminUser)
	assert.Nil(t, result.Error, "Should be able to create admin user")

	// Step 4: Create a student user
	studentUser := models.User{
		FullName:    "Test Student",
		Email:       studentEmail,
		PhoneNumber: "0987654321",
		Role:        "Student",
	}
	result = db.Create(&studentUser)
	assert.Nil(t, result.Error, "Should be able to create student user")

	// Step 5: Create student record and enroll in course
	student := models.Student{
		UserID:  studentUser.ID,
		Courses: []models.Course{course}, // Student enrolled in course with subject1
	}
	result = db.Create(&student)
	assert.Nil(t, result.Error, "Should be able to create student record")

	// Step 6: Test admin access - should get ALL subjects
	// Create a database plugin instance from the global db
	dbPlugin := dbPkg.NewServer(db)
	ctx := context.Background()

	adminSubjects, err := dbPlugin.GetSubjects(ctx, adminUser.ID)
	assert.Nil(t, err, "Admin should be able to get subjects")
	assert.GreaterOrEqual(t, len(adminSubjects), 2, "Admin should see all subjects (at least our 2 test subjects)")

	// Verify admin sees both test subjects
	subjectNames := make([]string, len(adminSubjects))
	for i, subject := range adminSubjects {
		subjectNames[i] = subject.Name
	}
	assert.Contains(t, subjectNames, subjectName1, "Admin should see subject 1")
	assert.Contains(t, subjectNames, subjectName2, "Admin should see subject 2")

	// Step 7: Test student access - should get only enrolled course subjects
	studentSubjects, err := dbPlugin.GetSubjects(ctx, studentUser.ID)
	assert.Nil(t, err, "Student should be able to get subjects")
	assert.Equal(t, 1, len(studentSubjects), "Student should see only 1 subject from enrolled course")
	assert.Equal(t, subjectName1, studentSubjects[0].Name, "Student should see only subject 1 from enrolled course")

	// Cleanup
	db.Delete(&student)
	db.Delete(&studentUser)
	db.Delete(&adminUser)
	db.Delete(&course)
	db.Delete(&subject1)
	db.Delete(&subject2)
}
