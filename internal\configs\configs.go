package configs

import (
	"os"
	"strconv"
	"strings"
	"time"

	"ziaacademy-backend/cmd/server/http"
	"ziaacademy-backend/db"
)

type env string

func (e env) String() string {
	return string(e)
}

const (
	EnvLocal      env = "local"
	EnvTest       env = "test"
	EnvStaging    env = "staging"
	EnvProduction env = "production"
)

// Configs struct handles all dependencies required for handling configurations
type Configs struct {
	Environment env
	AppName     string
	AppVersion  string
	Postgres    *db.Config
	Http        *http.Config
}

func (cfg *Configs) UserPostgresTable() string {
	return "users"
}

func loadEnv() env {
	switch env(os.Getenv("ENV")) {
	case EnvLocal:
		return EnvLocal
	case EnvTest:
		return EnvTest
	case EnvStaging:
		return EnvProduction
	case EnvProduction:
		return EnvProduction
	default:
		return EnvLocal
	}
}

// New returns an instance of Config with all the required dependencies initialized
func New(httpPort, pgServer, pgPort, pgDriver, pgDb, pgUsername,
	pgPwd string) (*Configs, error) {
	postgresConfig := &db.Config{
		Host:   pgServer,
		Port:   pgPort,
		Driver: pgDriver,

		StoreName: pgDb,
		Username:  pgUsername,
		Password:  pgPwd,

		ConnPoolSize: 24,
		ReadTimeout:  time.Second * 3,
		WriteTimeout: time.Second * 6,
		IdleTimeout:  time.Minute,
		DialTimeout:  time.Second * 3,
	}
	env := loadEnv()
	httpPortInt, _ := strconv.Atoi(httpPort)
	httpConfig := &http.Config{
		EnableAccessLog:   (env == EnvLocal) || (env == EnvTest),
		TemplatesBasePath: strings.TrimSpace(os.Getenv("TEMPLATES_BASEPATH")),
		Port:              uint16(httpPortInt),
		ReadTimeout:       time.Second * 5,
		WriteTimeout:      time.Second * 5,
		DialTimeout:       time.Second * 3,
	}
	return &Configs{
		Environment: env,
		AppName:     "ZIA_Academy",
		AppVersion:  "v1.0.0",
		Http:        httpConfig,
		Postgres:    postgresConfig,
	}, nil
}
