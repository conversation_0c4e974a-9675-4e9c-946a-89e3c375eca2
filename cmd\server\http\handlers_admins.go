package http

import (
	"context"
	"fmt"
	"log/slog"
	"net/http"
	"time"
	"ziaacademy-backend/internal/models"
	"ziaacademy-backend/internal/token"

	"github.com/gin-gonic/gin"
)

// CreateAdmin godoc
//
//	@Summary		Create Admin User
//	@Description	Create a new admin user with role "Admin"
//	@Description
//	@Description	Field Constraints:
//	@Description	- fullName: Required field
//	@Description	- email: Must be valid email format and unique across all users (required)
//	@Description	- phoneNumber: Must be unique across all users (required)
//	@Description	- password: Must be at least 6 characters long (required)
//	@Description	- contactAddress: Optional field
//	@Security       BearerAuth
//	@Param			admin	body	models.AdminForCreate	true	"Admin user details"
//	@Tags			admins
//	@Accept			json
//	@Produce		json
//	@Success		201	{object}	models.CreatedAdminResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		401	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/admins [post]
func (h *Handlers) CreateAdmin(ctx *gin.Context) {
	adminInput := new(models.AdminForCreate)
	if err := ctx.ShouldBindJSON(adminInput); err != nil {
		slog.Error("Failed to bind admin input", "error", err.Error())
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("Processing admin creation request", "email", adminInput.Email)

	// Create admin user model
	admin := models.User{
		FullName:       adminInput.FullName,
		Email:          adminInput.Email,
		PhoneNumber:    adminInput.PhoneNumber,
		ContactAddress: adminInput.ContactAddress,
		Role:           "Admin",
		PasswordHash:   adminInput.Password, // This will be hashed in the database layer
		EmailVerified:  false,
		PhoneVerified:  false,
	}

	createdAdmin, err := h.db.CreateAdmin(ctx.Request.Context(), &admin)
	if err != nil {
		slog.Error("Failed to create admin", "email", adminInput.Email,
			"error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	slog.Debug("Created admin", "email", createdAdmin.Email)

	// Generate JWT token for the new admin
	token, err := token.GenerateJWT(createdAdmin.ID)
	if err != nil {
		slog.Error("Failed to generate token", "email", adminInput.Email, "error", err.Error())
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	slog.Debug("Generated token", "email", adminInput.Email)

	// Return the token and simple admin details
	response := models.CreatedAdminResponse{
		Token: token,
		Admin: models.SimpleEntityResponse{
			ID:   createdAdmin.ID,
			Name: createdAdmin.FullName,
		},
	}
	ctx.JSON(http.StatusCreated, response)
}

// GetAdminUsers godoc
//
//	@Summary		Get Admin Users
//	@Description	Get a list of all admin users with their basic information (ID, email, phone number, contact address, and full name)
//	@Security       BearerAuth
//	@Tags			admins
//	@Accept			json
//	@Produce		json
//	@Success		200	{array}	models.AdminForGet
//	@Failure		401	{object}	HTTPError
//	@Failure		403	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/admins [get]
func (h *Handlers) GetAdminUsers(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("GetAdminUsers request started", "client_ip", clientIP)

	// Extract user ID from token for authorization check
	userID, err := token.ExtractTokenID(ctx)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetAdminUsers failed - invalid token",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
		return
	}

	// Check if the requesting user is an admin
	err = h.checkAdminPermission(ctx.Request.Context(), userID)
	if err != nil {
		duration := time.Since(start)
		slog.Warn("GetAdminUsers failed - insufficient permissions",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
		return
	}

	// Get admin users from database
	admins, err := h.db.GetAdminUsers(ctx.Request.Context())
	if err != nil {
		duration := time.Since(start)
		slog.Error("GetAdminUsers failed - database error",
			"user_id", userID,
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve admin users"})
		return
	}

	duration := time.Since(start)
	slog.Info("GetAdminUsers completed successfully",
		"user_id", userID,
		"client_ip", clientIP,
		"admin_count", len(admins),
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, admins)
}

// checkAdminPermission checks if the user has admin permissions
func (h *Handlers) checkAdminPermission(ctx context.Context, userID uint) error {
	// Get user by ID to check their role
	user, err := h.db.GetUserByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Check if user has Admin role
	if user.Role != "Admin" {
		return fmt.Errorf("user does not have admin privileges")
	}

	return nil
}
