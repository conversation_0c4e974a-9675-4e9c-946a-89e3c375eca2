package test

import (
	"encoding/json"
	"net/http"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateAdminStandalone(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping admin test")
		return
	}

	// Clean up before test
	if db != nil {
		db.Exec("DELETE FROM users WHERE email = ?", "<EMAIL>")
	}

	adminPayload := models.AdminForCreate{
		FullName:       "Test Admin",
		Email:          "<EMAIL>",
		PhoneNumber:    "5555555555",
		ContactAddress: "789 Admin Avenue",
		Password:       "adminpass123",
	}

	resp := requestExecutionHelper(http.MethodPost, "/api/admins", adminPayload)

	assert.Equal(t, http.StatusCreated, resp.Code)

	// Parse response
	var response map[string]interface{}
	err := json.Unmarshal(resp.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.NotNil(t, response["token"])
	assert.NotNil(t, response["admin"])

	// Verify admin details in response (now only ID and name)
	adminData := response["admin"].(map[string]interface{})
	assert.NotNil(t, adminData["id"])
	assert.Equal(t, "Test Admin", adminData["name"])

	// Verify admin was created in database if db is available
	if db != nil {
		var savedAdmin models.User
		result := db.First(&savedAdmin, "email = ?", "<EMAIL>")
		assert.Nil(t, result.Error)
		assert.Equal(t, "Test Admin", savedAdmin.FullName)
		assert.Equal(t, "<EMAIL>", savedAdmin.Email)
		assert.Equal(t, "5555555555", savedAdmin.PhoneNumber)
		assert.Equal(t, "Admin", savedAdmin.Role)
		assert.NotEmpty(t, savedAdmin.PasswordHash) // Password should be hashed

		// Cleanup
		db.Delete(&savedAdmin)
	}
}

func TestGetAdminUsersStandalone(t *testing.T) {
	// Skip if noAuthRouter is not initialized
	if noAuthRouter == nil {
		t.Skip("NoAuthRouter not initialized - skipping admin test")
		return
	}

	// Clean up before test
	if db != nil {
		db.Exec("DELETE FROM users WHERE email IN (?, ?)", "<EMAIL>", "<EMAIL>")
	}

	// Create test admin users
	admin1 := models.AdminForCreate{
		FullName:       "Test Admin 1",
		Email:          "<EMAIL>",
		PhoneNumber:    "5555555551",
		ContactAddress: "123 Admin Street",
		Password:       "adminpass123",
	}

	admin2 := models.AdminForCreate{
		FullName:       "Test Admin 2",
		Email:          "<EMAIL>",
		PhoneNumber:    "5555555552",
		ContactAddress: "456 Admin Avenue",
		Password:       "adminpass123",
	}

	// Create first admin
	resp1 := requestExecutionHelper(http.MethodPost, "/api/admins", admin1)
	assert.Equal(t, http.StatusCreated, resp1.Code)

	// Create second admin
	resp2 := requestExecutionHelper(http.MethodPost, "/api/admins", admin2)
	assert.Equal(t, http.StatusCreated, resp2.Code)

	// Extract token from first admin creation response for authentication
	var createResponse map[string]interface{}
	err := json.Unmarshal(resp1.Body.Bytes(), &createResponse)
	assert.Nil(t, err)
	token := createResponse["token"].(string)

	// Test GET /api/admins endpoint
	resp := authenticatedRequestHelper(http.MethodGet, "/api/admins", nil, token)
	assert.Equal(t, http.StatusOK, resp.Code)

	// Parse response
	var admins []models.AdminForGet
	err = json.Unmarshal(resp.Body.Bytes(), &admins)
	assert.Nil(t, err)
	assert.GreaterOrEqual(t, len(admins), 2) // Should have at least our 2 test admins

	// Verify that our test admins are in the response
	foundAdmin1 := false
	foundAdmin2 := false
	for _, admin := range admins {
		if admin.Email == "<EMAIL>" {
			foundAdmin1 = true
			assert.Equal(t, "Test Admin 1", admin.FullName)
			assert.Equal(t, "5555555551", admin.PhoneNumber)
			assert.Equal(t, "123 Admin Street", admin.ContactAddress)
		}
		if admin.Email == "<EMAIL>" {
			foundAdmin2 = true
			assert.Equal(t, "Test Admin 2", admin.FullName)
			assert.Equal(t, "5555555552", admin.PhoneNumber)
			assert.Equal(t, "456 Admin Avenue", admin.ContactAddress)
		}
	}
	assert.True(t, foundAdmin1, "Test Admin 1 should be found in response")
	assert.True(t, foundAdmin2, "Test Admin 2 should be found in response")

	// Cleanup
	if db != nil {
		db.Exec("DELETE FROM users WHERE email IN (?, ?)", "<EMAIL>", "<EMAIL>")
	}
}
