-- Migration: Add is_paid column to tests table
-- This column indicates whether a test is a paid test that can only be associated with courses
-- Free tests (is_paid = false) are accessible to all students regardless of course enrollment

-- Add the is_paid column with default value false
ALTER TABLE tests ADD COLUMN IF NOT EXISTS is_paid boolean NOT NULL DEFAULT false;

-- Add a comment to document the purpose of this column
COMMENT ON COLUMN tests.is_paid IS 'Indicates whether a test is paid (true) and can only be associated with courses, or free (false) and accessible to all students';

-- Create an index for efficient querying by is_paid status
CREATE INDEX IF NOT EXISTS idx_tests_is_paid ON tests (is_paid);

-- Create a composite index for efficient filtering by both active and is_paid status
CREATE INDEX IF NOT EXISTS idx_tests_active_is_paid ON tests (active, is_paid);
