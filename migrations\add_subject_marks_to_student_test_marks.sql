-- Migration to add subject_marks column to student_test_marks table
-- This column stores subject-wise and topic-wise marks breakdown in JSONB format
-- Run this script to update existing database schema

-- Add subject_marks column as JSONB to store subject-wise and topic-wise marks breakdown
ALTER TABLE student_test_marks ADD COLUMN IF NOT EXISTS subject_marks JSONB;

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns
WHERE table_name = 'student_test_marks' 
AND column_name = 'subject_marks'
ORDER BY column_name;
