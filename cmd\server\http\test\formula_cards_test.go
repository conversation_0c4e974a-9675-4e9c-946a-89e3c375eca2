package test

import (
	"encoding/json"
	"net/http"
	"net/url"
	"testing"
	"ziaacademy-backend/internal/models"

	"github.com/stretchr/testify/assert"
)

func TestCreateFormulaCards(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM formula_cards WHERE topic_id IN (SELECT id FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)))", "Physics Test Formula")
	db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", "Physics Test Formula")
	db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", "Physics Test Formula")
	db.Exec("DELETE FROM subjects WHERE name = ?", "Physics Test Formula")

	// Create the hierarchical structure: Subject -> Chapter -> Topic
	// 1. Create subject
	newSubject := models.SubjectForCreate{
		Name:        "Physics Test Formula",
		DisplayName: "Physics",
	}

	subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
	assert.Equal(t, http.StatusOK, subjectRR.Code)

	// 2. Create chapter
	newChapter := models.ChapterForCreate{
		Name:        "Mechanics Test Formula",
		DisplayName: "Mechanics",
		SubjectName: "Physics Test Formula",
	}

	chapterRR := requestExecutionHelper(http.MethodPost, "/api/chapters", newChapter)
	assert.Equal(t, http.StatusOK, chapterRR.Code)

	// 3. Create topic
	newTopic := models.TopicForCreate{
		Name:        "Laws of Motion Test Formula",
		ChapterName: "Mechanics Test Formula",
	}

	topicRR := requestExecutionHelper(http.MethodPost, "/api/topics", newTopic)
	assert.Equal(t, http.StatusOK, topicRR.Code)

	var createdTopic models.SimpleEntityResponse
	err := json.Unmarshal(topicRR.Body.Bytes(), &createdTopic)
	assert.Nil(t, err)

	// Now create formula cards for this topic
	formulaCardsInput := models.FormulaCardsForCreate{
		SubjectName: "Physics Test Formula",
		ChapterName: "Mechanics Test Formula",
		TopicName:   "Laws of Motion Test Formula",
		FormulaCards: []models.FormulaCardForCreate{
			{
				Name:     "Newton's Second Law",
				ImageUrl: "https://example.com/newton-second-law.png",
			},
			{
				Name:     "Kinetic Energy Formula",
				ImageUrl: "https://example.com/kinetic-energy.png",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)

	// Check status code
	assert.Equal(t, http.StatusOK, rr.Code)

	// Parse response (now returns array of SimpleEntityResponse)
	var response []models.SimpleEntityResponse
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)

	assert.Len(t, response, 2)
	assert.Equal(t, "Newton's Second Law", response[0].Name)
	assert.NotZero(t, response[0].ID)

	// Verify they were persisted in the database
	var fromDB []models.FormulaCard
	err = db.Where("topic_id = ?", createdTopic.ID).Find(&fromDB).Error
	assert.Nil(t, err)
	assert.Len(t, fromDB, 2)

	// Verify the first card details from database
	assert.Equal(t, "Newton's Second Law", fromDB[0].Name)
	assert.Equal(t, "https://example.com/newton-second-law.png", fromDB[0].ImageUrl)
	assert.Equal(t, createdTopic.ID, fromDB[0].TopicID)
}

func TestGetFormulaCardsBySubject(t *testing.T) {
	// Clean up before test - clean up all test subjects
	testSubjects := []string{"Mathematics Test Formula", "Physics Test Formula", "Chemistry Test Formula"}
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE topic_id IN (SELECT id FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)))", subjectName)
		db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}

	// Create hierarchical test data: subjects with chapters, topics, and formula cards
	testData := []struct {
		subjectName    string
		subjectDisplay string
		chapters       []struct {
			chapterName    string
			chapterDisplay string
			topics         []struct {
				topicName    string
				topicDisplay string
				cards        []models.FormulaCardForCreate
			}
		}
	}{
		{
			subjectName:    "Mathematics Test Formula",
			subjectDisplay: "Mathematics",
			chapters: []struct {
				chapterName    string
				chapterDisplay string
				topics         []struct {
					topicName    string
					topicDisplay string
					cards        []models.FormulaCardForCreate
				}
			}{
				{
					chapterName:    "Algebra Test Formula",
					chapterDisplay: "Algebra",
					topics: []struct {
						topicName    string
						topicDisplay string
						cards        []models.FormulaCardForCreate
					}{
						{
							topicName:    "Quadratic Equations Test Formula",
							topicDisplay: "Quadratic Equations",
							cards: []models.FormulaCardForCreate{
								{
									Name:     "Quadratic Formula",
									ImageUrl: "https://example.com/quadratic-formula.png",
								},
								{
									Name:     "Discriminant Formula",
									ImageUrl: "https://example.com/discriminant.png",
								},
							},
						},
					},
				},
				{
					chapterName:    "Geometry Test Formula",
					chapterDisplay: "Geometry",
					topics: []struct {
						topicName    string
						topicDisplay string
						cards        []models.FormulaCardForCreate
					}{
						{
							topicName:    "Triangles Test Formula",
							topicDisplay: "Triangles",
							cards: []models.FormulaCardForCreate{
								{
									Name:     "Pythagorean Theorem",
									ImageUrl: "https://example.com/pythagorean-theorem.png",
								},
							},
						},
					},
				},
			},
		},
		{
			subjectName:    "Physics Test Formula",
			subjectDisplay: "Physics",
			chapters: []struct {
				chapterName    string
				chapterDisplay string
				topics         []struct {
					topicName    string
					topicDisplay string
					cards        []models.FormulaCardForCreate
				}
			}{
				{
					chapterName:    "Mechanics Test Formula",
					chapterDisplay: "Mechanics",
					topics: []struct {
						topicName    string
						topicDisplay string
						cards        []models.FormulaCardForCreate
					}{
						{
							topicName:    "Laws of Motion Test Formula",
							topicDisplay: "Laws of Motion",
							cards: []models.FormulaCardForCreate{
								{
									Name:     "Newton's Second Law",
									ImageUrl: "https://example.com/newton-second-law.png",
								},
								{
									Name:     "Kinetic Energy Formula",
									ImageUrl: "https://example.com/kinetic-energy.png",
								},
							},
						},
					},
				},
			},
		},
	}

	// Create the hierarchical structure and formula cards
	for _, subjectData := range testData {
		// Create subject
		newSubject := models.SubjectForCreate{
			Name:        subjectData.subjectName,
			DisplayName: subjectData.subjectDisplay,
		}

		subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
		assert.Equal(t, http.StatusOK, subjectRR.Code)

		// Create chapters and topics for this subject
		for _, chapterData := range subjectData.chapters {
			// Create chapter
			newChapter := models.ChapterForCreate{
				Name:        chapterData.chapterName,
				DisplayName: chapterData.chapterDisplay,
				SubjectName: subjectData.subjectName,
			}

			chapterRR := requestExecutionHelper(http.MethodPost, "/api/chapters", newChapter)
			assert.Equal(t, http.StatusOK, chapterRR.Code)

			// Create topics for this chapter
			for _, topicData := range chapterData.topics {
				// Create topic
				newTopic := models.TopicForCreate{
					Name:        topicData.topicName,
					ChapterName: chapterData.chapterName,
				}

				topicRR := requestExecutionHelper(http.MethodPost, "/api/topics", newTopic)
				assert.Equal(t, http.StatusOK, topicRR.Code)

				// Create formula cards for this topic
				if len(topicData.cards) > 0 {
					formulaCardsInput := models.FormulaCardsForCreate{
						SubjectName:  subjectData.subjectName,
						ChapterName:  chapterData.chapterName,
						TopicName:    topicData.topicName,
						FormulaCards: topicData.cards,
					}

					createRR := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)
					assert.Equal(t, http.StatusOK, createRR.Code)
				}
			}
		}
	}

	// Test getting all formula cards organized hierarchically
	getRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards", nil)
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCardsBySubject
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	organizedCards := response["formula_cards_by_subject"]
	assert.Len(t, organizedCards, 2) // Should have 2 subjects

	// Create a map for easier verification
	subjectMap := make(map[string]models.FormulaCardsBySubject)
	for _, subjectCards := range organizedCards {
		subjectMap[subjectCards.SubjectName] = subjectCards
	}

	// Verify Mathematics subject
	mathSubject, exists := subjectMap["Mathematics Test Formula"]
	assert.True(t, exists, "Mathematics subject should exist")
	assert.Equal(t, 3, mathSubject.Count)  // Total cards in Mathematics
	assert.Len(t, mathSubject.Chapters, 2) // Should have 2 chapters

	// Verify Physics subject
	physicsSubject, exists := subjectMap["Physics Test Formula"]
	assert.True(t, exists, "Physics subject should exist")
	assert.Equal(t, 2, physicsSubject.Count)  // Total cards in Physics
	assert.Len(t, physicsSubject.Chapters, 1) // Should have 1 chapter

	// Test that subject_name parameter is ignored (endpoint always returns all subjects)
	t.Run("Verify subject_name parameter is ignored", func(t *testing.T) {
		// Try to get specific subject - should still return all subjects organized
		getURL := "/api/formula-cards?subject_name=" + url.QueryEscape("Mathematics Test Formula")
		getRR := requestExecutionHelper(http.MethodGet, getURL, nil)

		assert.Equal(t, http.StatusOK, getRR.Code)

		var response map[string][]models.FormulaCardsBySubject
		err := json.Unmarshal(getRR.Body.Bytes(), &response)
		assert.Nil(t, err)

		organizedCards := response["formula_cards_by_subject"]
		assert.Len(t, organizedCards, 2) // Should still return all 2 subjects, ignoring the parameter
	})

	// Clean up
	for _, subjectName := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE topic_id IN (SELECT id FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)))", subjectName)
		db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subjectName)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subjectName)
		db.Exec("DELETE FROM subjects WHERE name = ?", subjectName)
	}
}

func TestCreateFormulaCardsInvalidTopic(t *testing.T) {
	// Try to create formula cards for a non-existent topic
	formulaCardsInput := models.FormulaCardsForCreate{
		SubjectName: "NonExistentSubject",
		ChapterName: "NonExistentChapter",
		TopicName:   "NonExistentTopic",
		FormulaCards: []models.FormulaCardForCreate{
			{
				Name:     "Test Formula",
				ImageUrl: "https://example.com/test.png",
			},
		},
	}

	rr := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)

	// Should return 404 for non-existent topic/chapter/subject
	assert.Equal(t, http.StatusNotFound, rr.Code)

	var response map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	assert.Nil(t, err)
	assert.Equal(t, "Topic, chapter, or subject not found", response["error"])
}

func TestGetFormulaCardsBySubjectEmpty(t *testing.T) {
	// Clean up before test
	db.Exec("DELETE FROM formula_cards WHERE topic_id IN (SELECT id FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)))", "Biology Test Formula")
	db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", "Biology Test Formula")
	db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", "Biology Test Formula")
	db.Exec("DELETE FROM subjects WHERE name = ?", "Biology Test Formula")

	// Create a subject with chapter and topic but without formula cards
	newSubject := models.SubjectForCreate{
		Name:        "Biology Test Formula",
		DisplayName: "Biology",
	}

	subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
	assert.Equal(t, http.StatusOK, subjectRR.Code)

	// Create chapter
	newChapter := models.ChapterForCreate{
		Name:        "Cell Biology Test Formula",
		DisplayName: "Cell Biology",
		SubjectName: "Biology Test Formula",
	}

	chapterRR := requestExecutionHelper(http.MethodPost, "/api/chapters", newChapter)
	assert.Equal(t, http.StatusOK, chapterRR.Code)

	// Create topic
	newTopic := models.TopicForCreate{
		Name:        "Cell Structure Test Formula",
		ChapterName: "Cell Biology Test Formula",
	}

	topicRR := requestExecutionHelper(http.MethodPost, "/api/topics", newTopic)
	assert.Equal(t, http.StatusOK, topicRR.Code)

	// Get all formula cards (should not include Biology since it has no formula cards)
	getRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards", nil)

	// Check status code
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCardsBySubject
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	organizedCards := response["formula_cards_by_subject"]

	// Biology subject should not appear in the response since it has no formula cards
	for _, subjectCards := range organizedCards {
		assert.NotEqual(t, "Biology Test Formula", subjectCards.SubjectName, "Biology subject should not appear since it has no formula cards")
	}
}

func TestGetAllFormulaCardsOrganizedHierarchically(t *testing.T) {
	// Clean up before test - remove all existing formula cards and subjects to ensure clean state
	testSubjects := []string{"Physics Organized Test", "Chemistry Organized Test", "Math Organized Test"}
	for _, subject := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE topic_id IN (SELECT id FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)))", subject)
		db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subject)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subject)
		db.Exec("DELETE FROM subjects WHERE name = ?", subject)
	}

	// Create hierarchical test data
	testData := []struct {
		subjectName string
		chapterName string
		topicName   string
		cards       []models.FormulaCardForCreate
	}{
		{
			subjectName: "Physics Organized Test",
			chapterName: "Mechanics Organized Test",
			topicName:   "Laws of Motion Organized Test",
			cards: []models.FormulaCardForCreate{
				{Name: "Newton's Laws", ImageUrl: "https://example.com/newton.png"},
				{Name: "Energy Conservation", ImageUrl: "https://example.com/energy.png"},
			},
		},
		{
			subjectName: "Chemistry Organized Test",
			chapterName: "Atomic Structure Organized Test",
			topicName:   "Periodic Table Organized Test",
			cards: []models.FormulaCardForCreate{
				{Name: "Periodic Table", ImageUrl: "https://example.com/periodic.png"},
			},
		},
		{
			subjectName: "Math Organized Test",
			chapterName: "Algebra Organized Test",
			topicName:   "Equations Organized Test",
			cards: []models.FormulaCardForCreate{
				{Name: "Quadratic Formula", ImageUrl: "https://example.com/quadratic.png"},
				{Name: "Linear Equations", ImageUrl: "https://example.com/linear.png"},
			},
		},
	}

	// Create the hierarchical structure and formula cards
	for _, data := range testData {
		// Create subject
		newSubject := models.SubjectForCreate{
			Name:        data.subjectName,
			DisplayName: data.subjectName,
		}
		subjectRR := requestExecutionHelper(http.MethodPost, "/api/subjects", newSubject)
		assert.Equal(t, http.StatusOK, subjectRR.Code)

		// Create chapter
		newChapter := models.ChapterForCreate{
			Name:        data.chapterName,
			DisplayName: data.chapterName,
			SubjectName: data.subjectName,
		}
		chapterRR := requestExecutionHelper(http.MethodPost, "/api/chapters", newChapter)
		assert.Equal(t, http.StatusOK, chapterRR.Code)

		// Create topic
		newTopic := models.TopicForCreate{
			Name:        data.topicName,
			ChapterName: data.chapterName,
		}
		topicRR := requestExecutionHelper(http.MethodPost, "/api/topics", newTopic)
		assert.Equal(t, http.StatusOK, topicRR.Code)

		// Create formula cards for this topic
		formulaCardsInput := models.FormulaCardsForCreate{
			SubjectName:  data.subjectName,
			ChapterName:  data.chapterName,
			TopicName:    data.topicName,
			FormulaCards: data.cards,
		}
		cardsRR := requestExecutionHelper(http.MethodPost, "/api/formula-cards", formulaCardsInput)
		assert.Equal(t, http.StatusOK, cardsRR.Code)
	}

	// Test getting all formula cards organized hierarchically
	getRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards", nil)
	assert.Equal(t, http.StatusOK, getRR.Code)

	// Parse response
	var response map[string][]models.FormulaCardsBySubject
	err := json.Unmarshal(getRR.Body.Bytes(), &response)
	assert.Nil(t, err)

	organizedCards := response["formula_cards_by_subject"]
	assert.Len(t, organizedCards, 3) // Should have 3 subjects

	// Verify the hierarchical structure and content
	subjectMap := make(map[string]models.FormulaCardsBySubject)
	for _, subjectCards := range organizedCards {
		subjectMap[subjectCards.SubjectName] = subjectCards
	}

	// Verify Physics subject structure
	physicsSubject, exists := subjectMap["Physics Organized Test"]
	assert.True(t, exists)
	assert.Equal(t, 2, physicsSubject.Count)                            // Total cards in Physics
	assert.Len(t, physicsSubject.Chapters, 1)                           // Should have 1 chapter
	assert.Len(t, physicsSubject.Chapters[0].Topics, 1)                 // Should have 1 topic
	assert.Len(t, physicsSubject.Chapters[0].Topics[0].FormulaCards, 2) // Should have 2 cards

	// Verify Chemistry subject structure
	chemistrySubject, exists := subjectMap["Chemistry Organized Test"]
	assert.True(t, exists)
	assert.Equal(t, 1, chemistrySubject.Count)  // Total cards in Chemistry
	assert.Len(t, chemistrySubject.Chapters, 1) // Should have 1 chapter

	// Verify Math subject structure
	mathSubject, exists := subjectMap["Math Organized Test"]
	assert.True(t, exists)
	assert.Equal(t, 2, mathSubject.Count)  // Total cards in Math
	assert.Len(t, mathSubject.Chapters, 1) // Should have 1 chapter

	// Test that the endpoint ignores subject_name parameter and always returns organized data
	ignoredParamRR := requestExecutionHelper(http.MethodGet, "/api/formula-cards?subject_name=Physics+Organized+Test", nil)
	assert.Equal(t, http.StatusOK, ignoredParamRR.Code)

	var ignoredParamResponse map[string][]models.FormulaCardsBySubject
	err = json.Unmarshal(ignoredParamRR.Body.Bytes(), &ignoredParamResponse)
	assert.Nil(t, err)

	ignoredParamCards := ignoredParamResponse["formula_cards_by_subject"]
	assert.Len(t, ignoredParamCards, 3) // Should still return all 3 subjects, ignoring the parameter

	// Clean up
	for _, subject := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE topic_id IN (SELECT id FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)))", subject)
		db.Exec("DELETE FROM topics WHERE chapter_id IN (SELECT id FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?))", subject)
		db.Exec("DELETE FROM chapters WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subject)
		db.Exec("DELETE FROM subjects WHERE name = ?", subject)
	}
	for _, subject := range testSubjects {
		db.Exec("DELETE FROM formula_cards WHERE subject_id IN (SELECT id FROM subjects WHERE name = ?)", subject)
		db.Exec("DELETE FROM subjects WHERE name = ?", subject)
	}
}
