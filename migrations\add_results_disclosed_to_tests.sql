-- Migration: Add results_disclosed column to tests table
-- This column indicates whether test results can be disclosed to students

-- Add the results_disclosed column with default value false
ALTER TABLE tests ADD COLUMN IF NOT EXISTS results_disclosed boolean NOT NULL DEFAULT false;

-- Add a comment to document the purpose of this column
COMMENT ON COLUMN tests.results_disclosed IS 'Indicates whether test results can be disclosed to students';

-- Create an index for efficient querying by results_disclosed status
CREATE INDEX IF NOT EXISTS idx_tests_results_disclosed ON tests (results_disclosed);
