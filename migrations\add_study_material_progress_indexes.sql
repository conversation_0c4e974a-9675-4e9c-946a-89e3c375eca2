-- Migration: Add missing indexes for study_material_progresses table
-- This migration adds indexes to optimize queries for recently read study materials

-- Indexes for study_material_progresses table
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_student_id ON study_material_progresses(student_id);
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_study_material_id ON study_material_progresses(study_material_id);
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_last_read_at ON study_material_progresses(last_read_at DESC);
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_deleted_at ON study_material_progresses(deleted_at) WHERE deleted_at IS NOT NULL;

-- Composite index for efficient querying of recently read materials by student
CREATE INDEX IF NOT EXISTS idx_study_material_progresses_student_last_read ON study_material_progresses(student_id, last_read_at DESC);
