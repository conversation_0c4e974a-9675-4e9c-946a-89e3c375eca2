package http

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"io"
	"log/slog"
	"net/http"
	"time"

	"ziaacademy-backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CreateRazorpayOrder godoc
//
//	@Summary		Create Razorpay Order
//	@Description	Create a new Razorpay order for payment processing
//	@Description
//	@Description	Field Constraints:
//	@Description	- amount: Transaction amount in paise (required, minimum 1)
//	@Description	The API will generate a unique receipt ID and create a Razorpay order
//	@Security       BearerAuth
//	@Param			item	body	models.OrderForCreate	true	"order details"
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Success		200	{object}	models.OrderResponse
//	@Failure		400	{object}	HTTPError
//	@Failure		401	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/orders [post]
func (h *Handlers) CreateRazorpayOrder(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("CreateRazorpayOrder request started", "client_ip", clientIP)

	orderInput := new(models.OrderForCreate)
	if err := ctx.ShouldBindJSON(orderInput); err != nil {
		duration := time.Since(start)
		slog.Warn("CreateRazorpayOrder failed - invalid request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Generate a unique receipt ID using UUID
	receipt := uuid.New().String()

	slog.Debug("CreateRazorpayOrder request",
		"client_ip", clientIP,
		"amount", orderInput.Amount,
		"receipt", receipt,
	)

	// Call the CreateRazorpayOrder method from dbplugin
	orderID, err := h.db.CreateRazorpayOrder(orderInput.Amount, receipt)
	if err != nil {
		duration := time.Since(start)
		slog.Error("CreateRazorpayOrder failed - Razorpay API error",
			"client_ip", clientIP,
			"amount", orderInput.Amount,
			"receipt", receipt,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create Razorpay order"})
		return
	}

	duration := time.Since(start)
	slog.Info("CreateRazorpayOrder completed successfully",
		"client_ip", clientIP,
		"order_id", orderID,
		"amount", orderInput.Amount,
		"receipt", receipt,
		"duration_ms", duration.Milliseconds(),
	)

	// Return the order response
	response := models.OrderResponse{
		OrderID: orderID,
		Amount:  orderInput.Amount,
		Receipt: receipt,
	}

	ctx.JSON(http.StatusOK, response)
}

func verifySignature(body []byte, receivedSig, secret string) bool {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write(body)
	computedSig := hex.EncodeToString(h.Sum(nil))
	return computedSig == receivedSig
}

// RazorpayWebhookHandler godoc
//
//	@Summary		Handle Razorpay Webhook
//	@Description	Handle webhook notifications from Razorpay for payment events
//	@Tags			payments
//	@Accept			json
//	@Produce		json
//	@Success		200	{string}	string	"OK"
//	@Failure		400	{object}	HTTPError
//	@Failure		500	{object}	HTTPError
//	@Router			/webhook/razorpay [post]
func (h *Handlers) RazorpayWebhookHandler(ctx *gin.Context) {
	start := time.Now()
	clientIP := ctx.ClientIP()

	slog.Info("RazorpayWebhookHandler request started", "client_ip", clientIP)

	// Webhook secret - should be configured from environment
	secret := "your_webhook_secret" // TODO: Move to config

	// Read the request body
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		duration := time.Since(start)
		slog.Error("RazorpayWebhookHandler failed - unable to read request body",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Unable to read request body"})
		return
	}

	// Get the signature from headers
	signature := ctx.GetHeader("X-Razorpay-Signature")
	if signature == "" {
		duration := time.Since(start)
		slog.Warn("RazorpayWebhookHandler failed - missing signature header",
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing signature header"})
		return
	}

	// Verify the signature
	if !verifySignature(body, signature, secret) {
		duration := time.Since(start)
		slog.Warn("RazorpayWebhookHandler failed - invalid signature",
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid signature"})
		return
	}

	// Parse the payload
	var payload map[string]interface{}
	if err := json.Unmarshal(body, &payload); err != nil {
		duration := time.Since(start)
		slog.Error("RazorpayWebhookHandler failed - unable to parse payload",
			"client_ip", clientIP,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON payload"})
		return
	}

	// Extract event type
	event, ok := payload["event"].(string)
	if !ok {
		duration := time.Since(start)
		slog.Warn("RazorpayWebhookHandler failed - missing or invalid event type",
			"client_ip", clientIP,
			"duration_ms", duration.Milliseconds(),
		)
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing or invalid event type"})
		return
	}

	slog.Debug("RazorpayWebhookHandler processing event",
		"client_ip", clientIP,
		"event", event,
	)

	// Handle payment captured event
	if event == "payment.captured" {
		// Extract payment details from nested payload
		payloadData, ok := payload["payload"].(map[string]interface{})
		if !ok {
			duration := time.Since(start)
			slog.Error("RazorpayWebhookHandler failed - invalid payload structure",
				"client_ip", clientIP,
				"event", event,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payload structure"})
			return
		}

		paymentData, ok := payloadData["payment"].(map[string]interface{})
		if !ok {
			duration := time.Since(start)
			slog.Error("RazorpayWebhookHandler failed - missing payment data",
				"client_ip", clientIP,
				"event", event,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing payment data"})
			return
		}

		paymentID, ok := paymentData["id"].(string)
		if !ok {
			duration := time.Since(start)
			slog.Error("RazorpayWebhookHandler failed - missing payment ID",
				"client_ip", clientIP,
				"event", event,
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusBadRequest, gin.H{"error": "Missing payment ID"})
			return
		}

		// Verify payment status with Razorpay
		// Note: razorpayKey and razorpaySecret should be configured from environment
		razorpayKey := "rzp_test_YourKeyID" // TODO: Move to config
		razorpaySecret := "YourKeySecret"   // TODO: Move to config

		status, err := h.db.VerifyPaymentStatus(paymentID, razorpayKey, razorpaySecret)
		if err != nil {
			duration := time.Since(start)
			slog.Error("RazorpayWebhookHandler failed - payment verification error",
				"client_ip", clientIP,
				"event", event,
				"payment_id", paymentID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Payment verification failed"})
			return
		}

		slog.Info("RazorpayWebhookHandler payment verified",
			"client_ip", clientIP,
			"event", event,
			"payment_id", paymentID,
			"status", status,
		)

		// TODO: Update order/payment status in your database based on the verified status
		// This would typically involve updating a transaction record
	}

	duration := time.Since(start)
	slog.Info("RazorpayWebhookHandler completed successfully",
		"client_ip", clientIP,
		"event", event,
		"duration_ms", duration.Milliseconds(),
	)

	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}
