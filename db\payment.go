package db

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// Replace with your Razorpay credentials
const (
	razorpayKey    = "rzp_test_YourKeyID"
	razorpaySecret = "YourKeySecret"
)

type OrderRequest struct {
	Amount   int    `json:"amount"`   // amount in paise (e.g., ₹10 = 1000)
	Currency string `json:"currency"` // e.g., "INR"
	Receipt  string `json:"receipt"`
}

type OrderResponse struct {
	ID     string `json:"id"`
	Status string `json:"status"`
}

func (p *DbPlugin) CreateRazorpayOrder(amount int, receipt string) (string, error) {
	order := OrderRequest{
		Amount:   amount,
		Currency: "INR",
		Receipt:  receipt,
	}

	jsonData, _ := json.Marshal(order)
	req, err := http.NewRequest("POST", "https://api.razorpay.com/v1/orders", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}

	req.Header.Set("Content-Type", "application/json")

	auth := razorpayKey + ":" + razorpaySecret
	encodedAuth := base64.StdEncoding.EncodeToString([]byte(auth))
	req.Header.Set("Authorization", "Basic "+encodedAuth)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)

	if resp.StatusCode != 200 {
		return "", fmt.Errorf("razorpay error: %s", string(body))
	}

	var orderResp OrderResponse
	if err := json.Unmarshal(body, &orderResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// TODO: Store details in DB

	return orderResp.ID, nil
}

func (p *DbPlugin) VerifyPaymentStatus(paymentID, razorpayKey, razorpaySecret string) (string, error) {
	// Construct URL for Razorpay API
	url := fmt.Sprintf("https://api.razorpay.com/v1/payments/%s", paymentID)

	// Create request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", err
	}

	// Basic Auth for Razorpay API
	auth := base64.StdEncoding.EncodeToString([]byte(razorpayKey + ":" + razorpaySecret))
	req.Header.Set("Authorization", "Basic "+auth)

	// Send request to Razorpay API
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// Parse response JSON
	var paymentDetails map[string]interface{}
	if err := json.Unmarshal(body, &paymentDetails); err != nil {
		return "", err
	}

	// Extract payment status from response
	status := paymentDetails["status"].(string)

	// Return payment status
	return status, nil
}
