package db

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"
	"ziaacademy-backend/internal/models"
)

// AddComment adds a new comment to a video or study material
func (p *DbPlugin) AddComment(ctx context.Context, comment *models.Comment) (*models.Comment, error) {
	start := time.Now()
	slog.Info("Adding comment",
		"user_id", comment.UserID,
		"video_id", comment.VideoID,
		"material_id", comment.MaterialID,
	)

	// Validation: comment must belong to either a video or study material, but not both
	if (comment.VideoID == nil && comment.MaterialID == nil) || 
	   (comment.VideoID != nil && comment.MaterialID != nil) {
		duration := time.Since(start)
		slog.Error("Invalid comment target - must specify either video_id or material_id, but not both",
			"user_id", comment.UserID,
			"video_id", comment.VideoID,
			"material_id", comment.MaterialID,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("comment must belong to either a video or study material, but not both")
	}

	// Validate that the video or material exists
	if comment.VideoID != nil {
		var video models.Video
		if err := p.db.Where("id = ?", *comment.VideoID).First(&video).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Video not found for comment",
				"video_id", *comment.VideoID,
				"user_id", comment.UserID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("video with ID %d not found: %w", *comment.VideoID, err)
		}
	}

	if comment.MaterialID != nil {
		var material models.StudyMaterial
		if err := p.db.Where("id = ?", *comment.MaterialID).First(&material).Error; err != nil {
			duration := time.Since(start)
			slog.Error("Study material not found for comment",
				"material_id", *comment.MaterialID,
				"user_id", comment.UserID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("study material with ID %d not found: %w", *comment.MaterialID, err)
		}
	}

	// Create the comment
	if err := p.db.Create(comment).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to create comment",
			"user_id", comment.UserID,
			"video_id", comment.VideoID,
			"material_id", comment.MaterialID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Load the user information for the response
	if err := p.db.Preload("User").First(comment, comment.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load comment with user info",
			"comment_id", comment.ID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Comment created successfully",
		"comment_id", comment.ID,
		"user_id", comment.UserID,
		"video_id", comment.VideoID,
		"material_id", comment.MaterialID,
		"duration_ms", duration.Milliseconds(),
	)

	return comment, nil
}

// AddResponse adds a response to an existing comment
func (p *DbPlugin) AddResponse(ctx context.Context, response *models.Response) (*models.Response, error) {
	start := time.Now()
	slog.Info("Adding response to comment",
		"comment_id", response.CommentID,
		"user_id", response.UserID,
	)

	// Validate that the comment exists
	var comment models.Comment
	if err := p.db.Where("id = ?", response.CommentID).First(&comment).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Comment not found for response",
			"comment_id", response.CommentID,
			"user_id", response.UserID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("comment with ID %d not found: %w", response.CommentID, err)
	}

	// Create the response
	if err := p.db.Create(response).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to create response",
			"comment_id", response.CommentID,
			"user_id", response.UserID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Load the user information for the response
	if err := p.db.Preload("User").First(response, response.ID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to load response with user info",
			"response_id", response.ID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	duration := time.Since(start)
	slog.Info("Response created successfully",
		"response_id", response.ID,
		"comment_id", response.CommentID,
		"user_id", response.UserID,
		"duration_ms", duration.Milliseconds(),
	)

	return response, nil
}

// GetCommentsForVideo retrieves comments for a video with role-based filtering
func (p *DbPlugin) GetCommentsForVideo(ctx context.Context, videoID uint, userID uint) (*models.CommentsResponse, error) {
	start := time.Now()
	slog.Info("Getting comments for video",
		"video_id", videoID,
		"user_id", userID,
	)

	// Validate that the video exists
	var video models.Video
	if err := p.db.Where("id = ?", videoID).First(&video).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Video not found for getting comments",
			"video_id", videoID,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("video with ID %d not found: %w", videoID, err)
	}

	return p.getCommentsWithRoleFiltering(ctx, &videoID, nil, userID, start)
}

// GetCommentsForMaterial retrieves comments for a study material with role-based filtering
func (p *DbPlugin) GetCommentsForMaterial(ctx context.Context, materialID uint, userID uint) (*models.CommentsResponse, error) {
	start := time.Now()
	slog.Info("Getting comments for study material",
		"material_id", materialID,
		"user_id", userID,
	)

	// Validate that the study material exists
	var material models.StudyMaterial
	if err := p.db.Where("id = ?", materialID).First(&material).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Study material not found for getting comments",
			"material_id", materialID,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("study material with ID %d not found: %w", materialID, err)
	}

	return p.getCommentsWithRoleFiltering(ctx, nil, &materialID, userID, start)
}

// getCommentsWithRoleFiltering is a helper function that handles role-based comment filtering
func (p *DbPlugin) getCommentsWithRoleFiltering(ctx context.Context, videoID *uint, materialID *uint, userID uint, start time.Time) (*models.CommentsResponse, error) {
	// Get user to determine role
	var user models.User
	if err := p.db.Where("id = ?", userID).First(&user).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to find user for comment filtering",
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	slog.Debug("User found for comment filtering",
		"user_id", userID,
		"role", user.Role,
		"email", user.Email,
	)

	var comments []models.Comment
	query := p.db.Preload("User").Preload("Responses.User")

	// Apply filtering based on target (video or material)
	if videoID != nil {
		query = query.Where("video_id = ?", *videoID)
	} else if materialID != nil {
		query = query.Where("material_id = ?", *materialID)
	}

	// Apply role-based filtering
	if strings.ToLower(user.Role) == "student" {
		// For students, only show their own comments
		query = query.Where("user_id = ?", userID)
		slog.Debug("Applied student filtering - showing only user's comments", "user_id", userID)
	} else {
		// For admins, show all comments
		slog.Debug("Applied admin filtering - showing all comments", "user_id", userID)
	}

	// Execute query with ordering
	if err := query.Order("created_at DESC").Find(&comments).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve comments",
			"video_id", videoID,
			"material_id", materialID,
			"user_id", userID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, err
	}

	// Convert to response format
	var commentsWithResponses []models.CommentWithResponses
	for _, comment := range comments {
		var responses []models.ResponseOutput
		for _, response := range comment.Responses {
			responses = append(responses, models.ResponseOutput{
				ID:           response.ID,
				ResponseText: response.ResponseText,
				UserName:     response.User.FullName,
				UserID:       response.UserID,
				CreatedAt:    response.CreatedAt,
			})
		}

		commentsWithResponses = append(commentsWithResponses, models.CommentWithResponses{
			ID:          comment.ID,
			CommentText: comment.CommentText,
			UserName:    comment.User.FullName,
			UserID:      comment.UserID,
			VideoID:     comment.VideoID,
			MaterialID:  comment.MaterialID,
			CreatedAt:   comment.CreatedAt,
			Responses:   responses,
		})
	}

	duration := time.Since(start)
	slog.Info("Comments retrieved successfully",
		"video_id", videoID,
		"material_id", materialID,
		"user_id", userID,
		"user_role", user.Role,
		"comment_count", len(commentsWithResponses),
		"duration_ms", duration.Milliseconds(),
	)

	return &models.CommentsResponse{
		VideoID:    videoID,
		MaterialID: materialID,
		Comments:   commentsWithResponses,
	}, nil
}
